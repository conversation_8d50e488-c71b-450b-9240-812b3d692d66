# K230矩形跟踪系统使用指南

## 系统概述

这是一个优化的K230矩形跟踪系统，具有以下特点：
- **智能识别**: 多角度稳定识别矩形目标
- **自动跟踪**: PID控制云台自动跟踪目标中心
- **阈值调整**: 可视化阈值调整界面
- **数据持久化**: 阈值配置掉电不丢失
- **实时反馈**: 显示跟踪状态和调试信息

## 硬件要求

- K230开发板
- 摄像头模块
- LCD显示屏 (800x480) 或HDMI输出
- 触摸屏
- 按键 (连接到GPIO53)
- 云台控制器 (通过UART2通信)

## 软件功能

### 1. 主要模式

#### 跟踪模式 (默认)
- 系统启动后自动进入此模式
- 实时检测和跟踪矩形目标
- 显示绿色/黄色/橙色边框表示不同置信度
- 自动控制云台保持目标在中心

#### 阈值调整模式
- 按按键进入此模式
- 可视化调整二值化阈值
- 实时预览调整效果
- 支持保存配置

### 2. 操作方法

#### 基本操作
1. **启动系统**: 运行程序后自动进入跟踪模式
2. **调整阈值**: 按按键进入阈值调整界面
3. **保存设置**: 在调整界面点击"保存"按钮
4. **返回跟踪**: 点击"返回"按钮回到跟踪模式

#### 阈值调整界面
- **下限-**: 降低二值化下限阈值
- **下限+**: 提高二值化下限阈值  
- **上限-**: 降低二值化上限阈值
- **上限+**: 提高二值化上限阈值
- **归位**: 重置阈值为默认值 [0, 255]
- **保存**: 保存当前阈值到文件
- **返回**: 退出调整模式

### 3. 界面信息说明

#### 状态显示
- **Mode**: 当前模式 (TRACKING/SEARCHING/ADJUSTING)
- **FPS**: 实时帧率
- **Frame**: 当前帧数
- **Target**: 目标中心坐标 (仅跟踪时显示)
- **Error**: 偏差值 (仅跟踪时显示)
- **Gimbal**: 云台控制指令 (仅跟踪时显示)
- **Confidence**: 目标置信度 (仅跟踪时显示)
- **当前阈值**: 显示当前使用的阈值
- **丢失计数**: 目标丢失帧数统计

#### 视觉反馈
- **白色十字**: 图像中心标记
- **彩色矩形框**: 检测到的目标
  - 绿色: 高置信度 (>0.8)
  - 黄色: 中等置信度 (0.7-0.8)  
  - 橙色: 低置信度 (0.6-0.7)
- **红色圆点**: 目标中心点
- **红色连线**: 中心到目标的偏差线

## 优化特性

### 1. 多角度识别优化
- **多尺度检测**: 同时检测小、中、大三种尺度的矩形
- **形态学处理**: 腐蚀-膨胀-腐蚀序列去除噪声
- **置信度评估**: 综合面积、长宽比、位置等因素评分
- **最佳选择**: 从多个候选中选择置信度最高的目标

### 2. 稳定性优化
- **中心点平滑**: 历史加权平均减少抖动
- **连续跟踪**: 目标丢失时继续搜索，不切换模式
- **错误恢复**: 每个处理步骤都有异常处理机制

### 3. 用户体验优化
- **即开即用**: 启动后直接进入跟踪模式
- **配置持久化**: 阈值设置自动保存到文件
- **实时反馈**: 丰富的状态信息和视觉反馈

## 调试技巧

### 1. 阈值调整建议
- 在光线充足的环境下调整阈值
- 先调整下限，再调整上限
- 观察预览窗口中的二值化效果
- 目标应该显示为白色，背景为黑色
- 保存前确认效果满意

### 2. 识别问题排查
- **识别不稳定**: 重新调整阈值，确保目标清晰
- **误识别**: 提高置信度阈值或优化环境光线
- **跟踪抖动**: 检查PID参数设置
- **目标丢失**: 检查阈值设置和目标对比度

### 3. 性能优化
- 确保充足的光线条件
- 避免复杂背景干扰
- 定期清理摄像头镜头
- 监控系统温度避免过热

## 文件说明

- `untitled_1.py`: 主程序文件
- `/root/threshold_config.json`: 阈值配置文件 (自动生成)
- `优化说明.md`: 详细的优化说明文档
- `算法测试.py`: 算法验证测试脚本
- `使用指南.md`: 本使用指南

## 常见问题

**Q: 为什么有时候识别不到矩形？**
A: 可能是阈值设置不当，请进入阈值调整模式重新设置。

**Q: 跟踪时云台抖动怎么办？**
A: 可以调整PID参数，或者检查目标是否清晰稳定。

**Q: 阈值设置会丢失吗？**
A: 不会，系统会自动保存到配置文件中。

**Q: 如何提高识别准确率？**
A: 确保良好的光线条件，调整合适的阈值，避免背景干扰。

## 技术支持

如有问题请检查：
1. 硬件连接是否正确
2. 阈值设置是否合适
3. 环境光线是否充足
4. 系统日志中的错误信息
