# K230矩形跟踪系统优化说明

## 主要优化内容

### 1. 按键逻辑优化
- **原来**: 长按屏幕进入阈值调整，按键开始跟踪
- **现在**: 按键进入阈值调整模式，默认一直处于矩形识别模式
- **优势**: 更符合用户需求，无需手动启动跟踪

### 2. 阈值持久化功能
- **新增功能**: 阈值配置自动保存到文件 `/root/threshold_config.json`
- **实现方式**: 
  - `load_threshold_config()`: 启动时自动加载保存的阈值
  - `save_threshold_config()`: 调整阈值后自动保存
- **优势**: 掉电不丢失，无需重复调整

### 3. 多角度稳定识别算法优化

#### 3.1 高级矩形检测器 (AdvancedRectDetector)
- **置信度评估**: 综合考虑面积、长宽比、位置等因素
- **历史平滑**: 保存历史中心点，使用加权平均减少抖动
- **最佳矩形选择**: 从多个候选中选择置信度最高的矩形

#### 3.2 多尺度检测
- **小矩形检测**: threshold=3000，适合远距离或小目标
- **中等矩形检测**: threshold=8000，适合中等距离
- **大矩形检测**: threshold=15000，适合近距离或大目标

#### 3.3 形态学优化
- **噪声处理**: 腐蚀→膨胀→腐蚀序列，去除噪声并增强连接
- **连接增强**: 增加膨胀操作，提高不完整矩形的识别率

#### 3.4 置信度可视化
- **绿色**: 高置信度 (>0.8)
- **黄色**: 中等置信度 (0.7-0.8)
- **橙色**: 低置信度 (0.6-0.7)

### 4. 系统稳定性优化
- **错误处理**: 每个检测步骤都有异常处理
- **目标丢失**: 不再切换到待机模式，继续在跟踪模式中搜索
- **性能监控**: 显示置信度、丢失计数等调试信息

## 算法原理

### 置信度计算公式
```
confidence = area_score * 0.4 + ratio_score * 0.4 + position_score * 0.2
```

- **area_score**: 面积适中性评分
- **ratio_score**: 长宽比合理性评分  
- **position_score**: 距离中心的位置评分

### 平滑算法
使用加权移动平均，最新的点权重更大：
```
smooth_center = Σ(center_i * weight_i) / Σ(weight_i)
```

## 使用说明

1. **启动**: 程序启动后自动进入矩形跟踪模式
2. **调阈值**: 按按键进入阈值调整界面
3. **保存**: 在阈值调整界面点击"保存"按钮，阈值自动持久化
4. **返回**: 点击"返回"按钮回到跟踪模式

## 预期效果

1. **多角度识别**: 侧面、侧下等角度的矩形识别更稳定
2. **减少抖动**: 中心点平滑算法减少云台抖动
3. **提高准确性**: 置信度评估确保选择最佳目标
4. **用户友好**: 阈值掉电不丢失，操作更简便

## 技术特点

- **自适应**: 多尺度、多阈值检测适应不同场景
- **鲁棒性**: 形态学操作和置信度评估提高抗干扰能力
- **实时性**: 优化算法保持实时性能
- **可维护**: 模块化设计，易于调试和扩展
