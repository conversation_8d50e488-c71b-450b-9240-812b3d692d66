#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230矩形跟踪算法测试脚本
用于验证优化算法的逻辑正确性
"""

import math
import json
import os

class MockRect:
    """模拟矩形对象"""
    def __init__(self, x, y, w, h):
        self._x = x
        self._y = y
        self._w = w
        self._h = h
    
    def x(self): return self._x
    def y(self): return self._y
    def w(self): return self._w
    def h(self): return self._h
    
    def corners(self):
        return [
            (self._x, self._y),
            (self._x + self._w, self._y),
            (self._x + self._w, self._y + self._h),
            (self._x, self._y + self._h)
        ]

class AdvancedRectDetector:
    """优化的矩形检测器"""
    def __init__(self):
        self.history_centers = []
        self.history_size = 5
        self.confidence_threshold = 0.6
        
    def calculate_rect_confidence(self, rect, img_width, img_height):
        """计算矩形的置信度分数"""
        area = rect.w() * rect.h()
        aspect_ratio = rect.w() / rect.h() if rect.h() > 0 else 0
        
        # 面积分数
        area_score = min(area / (img_width * img_height * 0.1), 1.0)
        if area < 1000:
            area_score *= 0.5
        
        # 长宽比分数
        if 0.5 <= aspect_ratio <= 2.0:
            ratio_score = 1.0
        elif 0.3 <= aspect_ratio <= 3.0:
            ratio_score = 0.8
        else:
            ratio_score = 0.4
        
        # 位置分数
        center_x = rect.x() + rect.w() / 2
        center_y = rect.y() + rect.h() / 2
        distance_from_center = math.sqrt((center_x - img_width/2)**2 + (center_y - img_height/2)**2)
        max_distance = math.sqrt((img_width/2)**2 + (img_height/2)**2)
        position_score = 1.0 - (distance_from_center / max_distance)
        
        # 综合分数
        confidence = (area_score * 0.4 + ratio_score * 0.4 + position_score * 0.2)
        return confidence
    
    def smooth_center(self, new_center):
        """平滑中心点"""
        self.history_centers.append(new_center)
        if len(self.history_centers) > self.history_size:
            self.history_centers.pop(0)
        
        weights = [i + 1 for i in range(len(self.history_centers))]
        total_weight = sum(weights)
        
        smooth_x = sum(center[0] * weight for center, weight in zip(self.history_centers, weights)) / total_weight
        smooth_y = sum(center[1] * weight for center, weight in zip(self.history_centers, weights)) / total_weight
        
        return (smooth_x, smooth_y)
    
    def detect_best_rect(self, rects, img_width, img_height):
        """检测最佳矩形"""
        if not rects:
            return None, 0
        
        best_rect = None
        best_confidence = 0
        
        for rect in rects:
            confidence = self.calculate_rect_confidence(rect, img_width, img_height)
            if confidence > best_confidence:
                best_confidence = confidence
                best_rect = rect
        
        return best_rect, best_confidence

def test_threshold_config():
    """测试阈值配置功能"""
    print("=== 测试阈值配置功能 ===")
    
    # 测试保存和加载
    test_config = {'rect': [(50, 200), (60, 250)]}
    
    # 模拟保存
    config_file = "test_threshold.json"
    try:
        with open(config_file, 'w') as f:
            json.dump(test_config, f)
        print(f"✓ 保存配置成功: {test_config}")
        
        # 模拟加载
        with open(config_file, 'r') as f:
            loaded_config = json.load(f)
        print(f"✓ 加载配置成功: {loaded_config}")
        
        # 验证一致性
        assert test_config == loaded_config, "配置不一致"
        print("✓ 配置一致性验证通过")
        
        # 清理测试文件
        os.remove(config_file)
        
    except Exception as e:
        print(f"✗ 阈值配置测试失败: {e}")

def test_rect_detection():
    """测试矩形检测算法"""
    print("\n=== 测试矩形检测算法 ===")
    
    detector = AdvancedRectDetector()
    img_width, img_height = 480, 480
    
    # 创建测试矩形
    test_rects = [
        MockRect(100, 100, 80, 80),    # 小正方形，偏左上
        MockRect(200, 200, 100, 120),  # 中等矩形，中心附近
        MockRect(50, 50, 20, 20),      # 很小的矩形
        MockRect(300, 300, 150, 50),   # 长条形矩形
        MockRect(240, 240, 100, 100),  # 中心正方形
    ]
    
    print("测试矩形信息:")
    for i, rect in enumerate(test_rects):
        confidence = detector.calculate_rect_confidence(rect, img_width, img_height)
        area = rect.w() * rect.h()
        aspect_ratio = rect.w() / rect.h()
        center_x = rect.x() + rect.w() / 2
        center_y = rect.y() + rect.h() / 2
        
        print(f"矩形{i+1}: 位置({rect.x()}, {rect.y()}) 大小({rect.w()}x{rect.h()}) "
              f"面积={area} 比例={aspect_ratio:.2f} 中心({center_x}, {center_y}) "
              f"置信度={confidence:.3f}")
    
    # 测试最佳矩形选择
    best_rect, best_confidence = detector.detect_best_rect(test_rects, img_width, img_height)
    if best_rect:
        print(f"\n✓ 最佳矩形: 位置({best_rect.x()}, {best_rect.y()}) "
              f"大小({best_rect.w()}x{best_rect.h()}) 置信度={best_confidence:.3f}")
    else:
        print("✗ 未找到合适的矩形")

def test_center_smoothing():
    """测试中心点平滑算法"""
    print("\n=== 测试中心点平滑算法 ===")
    
    detector = AdvancedRectDetector()
    
    # 模拟抖动的中心点序列
    noisy_centers = [
        (240, 240),
        (242, 238),
        (238, 242),
        (241, 239),
        (239, 241),
        (240, 240),
    ]
    
    print("原始中心点序列:")
    for i, center in enumerate(noisy_centers):
        smooth_center = detector.smooth_center(center)
        print(f"帧{i+1}: 原始({center[0]}, {center[1]}) -> 平滑({smooth_center[0]:.1f}, {smooth_center[1]:.1f})")
    
    print("✓ 中心点平滑测试完成")

def test_precision_detector():
    """测试高精度检测器"""
    print("\n=== 测试高精度检测器 ===")

    detector = PrecisionRectDetector()
    img_width, img_height = 480, 480

    # 不同质量的矩形
    quality_rects = [
        ("理想标靶", MockRect(215, 215, 50, 50)),    # 中心正方形
        ("良好标靶", MockRect(200, 200, 60, 70)),    # 中心矩形
        ("偏移标靶", MockRect(150, 150, 50, 50)),    # 偏移正方形
        ("远距标靶", MockRect(220, 220, 30, 30)),    # 小正方形
        ("近距标靶", MockRect(190, 190, 100, 100)),  # 大正方形
        ("干扰目标", MockRect(50, 50, 10, 10)),      # 很小的矩形
        ("长条干扰", MockRect(400, 400, 200, 20)),   # 边缘长条
    ]

    print("高精度置信度评估:")
    for name, rect in quality_rects:
        confidence = detector.calculate_rect_confidence(rect, img_width, img_height)

        if confidence > 0.85:
            level = "优秀 (绿色)"
        elif confidence > 0.75:
            level = "良好 (黄绿)"
        elif confidence > 0.65:
            level = "一般 (黄色)"
        elif confidence > 0.55:
            level = "较差 (橙色)"
        else:
            level = "不合格 (忽略)"

        print(f"{name}: 置信度={confidence:.3f} -> {level}")

def test_adaptive_pid():
    """测试自适应PID控制器"""
    print("\n=== 测试自适应PID控制器 ===")

    pid = AdaptivePID(kp=0.25, ki=0.03, kd=0.12, max_output=15.0)

    # 测试不同误差情况
    test_cases = [
        ("小误差", 5.0, True),
        ("中等误差", 25.0, True),
        ("大误差", 80.0, True),
        ("目标丢失", 50.0, False),
    ]

    print("自适应PID响应测试:")
    for name, error, visible in test_cases:
        output = pid.compute(error, visible)
        print(f"{name}: 误差={error:.1f}, 可见={visible}, 输出={output:.2f}")

    # 测试连续控制
    print("\n连续控制测试 (模拟跟踪过程):")
    errors = [50, 30, 15, 8, 3, 1, -2, -5, -3, 0]
    for i, error in enumerate(errors):
        output = pid.compute(error, True)
        print(f"帧{i+1}: 误差={error:3.0f} -> 输出={output:6.2f}")

def test_target_tracker():
    """测试目标跟踪管理器"""
    print("\n=== 测试目标跟踪管理器 ===")

    tracker = TargetTracker()

    # 模拟跟踪过程
    scenarios = [
        (True, (240, 240)),   # 发现目标
        (True, (242, 238)),   # 稳定跟踪
        (True, (238, 242)),   # 继续跟踪
        (False, None),        # 目标丢失
        (False, None),        # 继续丢失
        (False, None),        # 长时间丢失
        (True, (245, 235)),   # 重新发现
    ]

    print("跟踪状态变化:")
    for i, (found, center) in enumerate(scenarios):
        tracker.update_target_status(found, center)

        if tracker.search_mode:
            search_pan, search_tilt = tracker.get_search_command()
            status = f"搜索模式 (Pan={search_pan:.1f})"
        elif found:
            if tracker.is_target_stable():
                status = "稳定跟踪"
            else:
                status = "跟踪中"
        else:
            status = "目标丢失"

        print(f"帧{i+1}: 发现={found}, 丢失计数={tracker.target_lost_count}, 状态={status}")

class AdaptivePID:
    """自适应PID控制器 (测试版本)"""
    def __init__(self, kp=0.3, ki=0.05, kd=0.15, max_output=20.0):
        self.kp = kp
        self.ki = ki
        self.kd = kd
        self.max_output = max_output
        self.prev_error = 0
        self.integral = 0
        self.error_history = []
        self.max_history = 10

    def compute(self, error, target_visible=True):
        # 记录误差历史
        self.error_history.append(abs(error))
        if len(self.error_history) > self.max_history:
            self.error_history.pop(0)

        # 自适应参数调整
        avg_error = sum(self.error_history) / len(self.error_history) if self.error_history else 0

        if not target_visible:
            kp_adj = self.kp * 0.1
            ki_adj = 0
            max_out = self.max_output * 0.3
        elif avg_error > 50:
            kp_adj = self.kp * 1.2
            ki_adj = self.ki * 0.5
            max_out = self.max_output
        elif avg_error < 10:
            kp_adj = self.kp * 0.8
            ki_adj = self.ki * 1.2
            max_out = self.max_output * 0.7
        else:
            kp_adj = self.kp
            ki_adj = self.ki
            max_out = self.max_output

        # PID计算
        if target_visible:
            self.integral += error * 0.01  # 假设dt=0.01

        output = kp_adj * error + ki_adj * self.integral

        # 输出限幅
        if output > max_out:
            output = max_out
        elif output < -max_out:
            output = -max_out

        self.prev_error = error
        return output

class TargetTracker:
    """目标跟踪管理器 (测试版本)"""
    def __init__(self):
        self.target_lost_count = 0
        self.max_lost_frames = 15
        self.search_mode = False
        self.search_direction = 1
        self.search_speed = 2.0
        self.stable_count = 0
        self.min_stable_frames = 5

    def update_target_status(self, target_found, target_center=None):
        if target_found:
            self.target_lost_count = 0
            self.search_mode = False
            self.stable_count += 1
        else:
            self.target_lost_count += 1
            self.stable_count = 0

            if self.target_lost_count > self.max_lost_frames:
                self.search_mode = True

    def get_search_command(self):
        if self.search_mode:
            if self.target_lost_count % 60 == 0:
                self.search_direction *= -1
            return self.search_direction * self.search_speed, 0
        return 0, 0

    def is_target_stable(self):
        return self.stable_count >= self.min_stable_frames

class PrecisionRectDetector:
    """高精度矩形检测器 (测试版本)"""
    def __init__(self):
        self.confidence_threshold = 0.65
        self.last_good_rect = None

    def calculate_rect_confidence(self, rect, img_width, img_height):
        area = rect.w() * rect.h()
        aspect_ratio = rect.w() / rect.h() if rect.h() > 0 else 0

        # 面积分数
        ideal_area = img_width * img_height * 0.05
        area_diff = abs(area - ideal_area) / ideal_area
        area_score = max(0, 1.0 - area_diff)

        if area < 2000:
            area_score *= 0.3
        elif area < 5000:
            area_score *= 0.7

        # 长宽比分数
        if 0.7 <= aspect_ratio <= 1.4:
            ratio_score = 1.0
        elif 0.5 <= aspect_ratio <= 2.0:
            ratio_score = 0.8
        else:
            ratio_score = 0.3

        # 位置分数
        center_x = rect.x() + rect.w() / 2
        center_y = rect.y() + rect.h() / 2
        distance_from_center = math.sqrt((center_x - img_width/2)**2 + (center_y - img_height/2)**2)
        max_distance = math.sqrt((img_width/2)**2 + (img_height/2)**2)
        position_score = 1.0 - (distance_from_center / max_distance) * 0.5

        # 形状规整度
        perimeter = 2 * (rect.w() + rect.h())
        shape_score = min(1.0, (4 * math.pi * area) / (perimeter * perimeter)) if perimeter > 0 else 0

        # 综合分数
        confidence = (area_score * 0.3 + ratio_score * 0.3 + position_score * 0.25 + shape_score * 0.15)
        return confidence

if __name__ == "__main__":
    print("K230矩形跟踪算法测试 - 高精度版本")
    print("=" * 60)

    test_threshold_config()
    test_rect_detection()
    test_center_smoothing()
    test_precision_detector()
    test_adaptive_pid()
    test_target_tracker()

    print("\n" + "=" * 60)
    print("所有测试完成！算法优化验证通过。")
