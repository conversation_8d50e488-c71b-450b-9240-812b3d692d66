#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
K230矩形跟踪算法测试脚本
用于验证优化算法的逻辑正确性
"""

import math
import json
import os

class MockRect:
    """模拟矩形对象"""
    def __init__(self, x, y, w, h):
        self._x = x
        self._y = y
        self._w = w
        self._h = h
    
    def x(self): return self._x
    def y(self): return self._y
    def w(self): return self._w
    def h(self): return self._h
    
    def corners(self):
        return [
            (self._x, self._y),
            (self._x + self._w, self._y),
            (self._x + self._w, self._y + self._h),
            (self._x, self._y + self._h)
        ]

class AdvancedRectDetector:
    """优化的矩形检测器"""
    def __init__(self):
        self.history_centers = []
        self.history_size = 5
        self.confidence_threshold = 0.6
        
    def calculate_rect_confidence(self, rect, img_width, img_height):
        """计算矩形的置信度分数"""
        area = rect.w() * rect.h()
        aspect_ratio = rect.w() / rect.h() if rect.h() > 0 else 0
        
        # 面积分数
        area_score = min(area / (img_width * img_height * 0.1), 1.0)
        if area < 1000:
            area_score *= 0.5
        
        # 长宽比分数
        if 0.5 <= aspect_ratio <= 2.0:
            ratio_score = 1.0
        elif 0.3 <= aspect_ratio <= 3.0:
            ratio_score = 0.8
        else:
            ratio_score = 0.4
        
        # 位置分数
        center_x = rect.x() + rect.w() / 2
        center_y = rect.y() + rect.h() / 2
        distance_from_center = math.sqrt((center_x - img_width/2)**2 + (center_y - img_height/2)**2)
        max_distance = math.sqrt((img_width/2)**2 + (img_height/2)**2)
        position_score = 1.0 - (distance_from_center / max_distance)
        
        # 综合分数
        confidence = (area_score * 0.4 + ratio_score * 0.4 + position_score * 0.2)
        return confidence
    
    def smooth_center(self, new_center):
        """平滑中心点"""
        self.history_centers.append(new_center)
        if len(self.history_centers) > self.history_size:
            self.history_centers.pop(0)
        
        weights = [i + 1 for i in range(len(self.history_centers))]
        total_weight = sum(weights)
        
        smooth_x = sum(center[0] * weight for center, weight in zip(self.history_centers, weights)) / total_weight
        smooth_y = sum(center[1] * weight for center, weight in zip(self.history_centers, weights)) / total_weight
        
        return (smooth_x, smooth_y)
    
    def detect_best_rect(self, rects, img_width, img_height):
        """检测最佳矩形"""
        if not rects:
            return None, 0
        
        best_rect = None
        best_confidence = 0
        
        for rect in rects:
            confidence = self.calculate_rect_confidence(rect, img_width, img_height)
            if confidence > best_confidence:
                best_confidence = confidence
                best_rect = rect
        
        return best_rect, best_confidence

def test_threshold_config():
    """测试阈值配置功能"""
    print("=== 测试阈值配置功能 ===")
    
    # 测试保存和加载
    test_config = {'rect': [(50, 200), (60, 250)]}
    
    # 模拟保存
    config_file = "test_threshold.json"
    try:
        with open(config_file, 'w') as f:
            json.dump(test_config, f)
        print(f"✓ 保存配置成功: {test_config}")
        
        # 模拟加载
        with open(config_file, 'r') as f:
            loaded_config = json.load(f)
        print(f"✓ 加载配置成功: {loaded_config}")
        
        # 验证一致性
        assert test_config == loaded_config, "配置不一致"
        print("✓ 配置一致性验证通过")
        
        # 清理测试文件
        os.remove(config_file)
        
    except Exception as e:
        print(f"✗ 阈值配置测试失败: {e}")

def test_rect_detection():
    """测试矩形检测算法"""
    print("\n=== 测试矩形检测算法 ===")
    
    detector = AdvancedRectDetector()
    img_width, img_height = 480, 480
    
    # 创建测试矩形
    test_rects = [
        MockRect(100, 100, 80, 80),    # 小正方形，偏左上
        MockRect(200, 200, 100, 120),  # 中等矩形，中心附近
        MockRect(50, 50, 20, 20),      # 很小的矩形
        MockRect(300, 300, 150, 50),   # 长条形矩形
        MockRect(240, 240, 100, 100),  # 中心正方形
    ]
    
    print("测试矩形信息:")
    for i, rect in enumerate(test_rects):
        confidence = detector.calculate_rect_confidence(rect, img_width, img_height)
        area = rect.w() * rect.h()
        aspect_ratio = rect.w() / rect.h()
        center_x = rect.x() + rect.w() / 2
        center_y = rect.y() + rect.h() / 2
        
        print(f"矩形{i+1}: 位置({rect.x()}, {rect.y()}) 大小({rect.w()}x{rect.h()}) "
              f"面积={area} 比例={aspect_ratio:.2f} 中心({center_x}, {center_y}) "
              f"置信度={confidence:.3f}")
    
    # 测试最佳矩形选择
    best_rect, best_confidence = detector.detect_best_rect(test_rects, img_width, img_height)
    if best_rect:
        print(f"\n✓ 最佳矩形: 位置({best_rect.x()}, {best_rect.y()}) "
              f"大小({best_rect.w()}x{best_rect.h()}) 置信度={best_confidence:.3f}")
    else:
        print("✗ 未找到合适的矩形")

def test_center_smoothing():
    """测试中心点平滑算法"""
    print("\n=== 测试中心点平滑算法 ===")
    
    detector = AdvancedRectDetector()
    
    # 模拟抖动的中心点序列
    noisy_centers = [
        (240, 240),
        (242, 238),
        (238, 242),
        (241, 239),
        (239, 241),
        (240, 240),
    ]
    
    print("原始中心点序列:")
    for i, center in enumerate(noisy_centers):
        smooth_center = detector.smooth_center(center)
        print(f"帧{i+1}: 原始({center[0]}, {center[1]}) -> 平滑({smooth_center[0]:.1f}, {smooth_center[1]:.1f})")
    
    print("✓ 中心点平滑测试完成")

def test_confidence_levels():
    """测试不同置信度级别"""
    print("\n=== 测试置信度级别 ===")
    
    detector = AdvancedRectDetector()
    img_width, img_height = 480, 480
    
    # 不同质量的矩形
    quality_rects = [
        ("高质量", MockRect(215, 215, 50, 50)),    # 中心小正方形
        ("中等质量", MockRect(100, 100, 80, 120)), # 偏离中心的矩形
        ("低质量", MockRect(50, 50, 10, 10)),      # 很小的矩形
        ("极低质量", MockRect(400, 400, 200, 20)), # 边缘长条
    ]
    
    for name, rect in quality_rects:
        confidence = detector.calculate_rect_confidence(rect, img_width, img_height)
        
        if confidence > 0.8:
            level = "高置信度 (绿色)"
        elif confidence > 0.7:
            level = "中等置信度 (黄色)"
        elif confidence > 0.6:
            level = "低置信度 (橙色)"
        else:
            level = "不合格 (忽略)"
        
        print(f"{name}: 置信度={confidence:.3f} -> {level}")

if __name__ == "__main__":
    print("K230矩形跟踪算法测试")
    print("=" * 50)
    
    test_threshold_config()
    test_rect_detection()
    test_center_smoothing()
    test_confidence_levels()
    
    print("\n" + "=" * 50)
    print("所有测试完成！")
