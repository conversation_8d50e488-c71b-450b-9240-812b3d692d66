# K230矩形跟踪系统 - 集成脱机调阈值功能
# 功能：矩形跟踪 + 脱机调矩形阈值
# 长按屏幕进入阈值调整模式，按键开始跟踪

import time
import os

# 导入模块
from media.sensor import *
from media.display import *
from media.media import *
from machine import FPIOA, Pin, UART, TOUCH
from time import ticks_ms
import image

print("模块导入成功")

# 全局变量
sensor = None
running = True

def cleanup():
    global sensor, running
    print("开始清理...")
    running = False
    try:
        if sensor:
            sensor.stop()
            print("摄像头已停止")
        Display.deinit()
        print("显示系统已关闭")
        MediaManager.deinit()
        print("媒体管理器已关闭")
    except Exception as e:
        print(f"清理过程中出错: {e}")
    print("清理完成")

def main():
    global sensor, running

    try:
        print("=== K230矩形跟踪系统 (集成调阈值功能) ===")

        # 1. 摄像头初始化
        print("初始化摄像头...")
        sensor = Sensor(width=1920, height=1080)
        sensor.reset()
        sensor.set_framesize(width=1920, height=1080)
        sensor.set_pixformat(Sensor.RGB565)

        # 2. 显示初始化
        print("初始化显示...")
        try:
            Display.init(Display.ST7701, to_ide=False, width=800, height=480)  # 使用LCD屏幕
            print("LCD显示初始化完成")
        except:
            # 备用显示方式
            Display.init(Display.LT9611, to_ide=False)
            print("备用显示初始化完成")

        # 3. 媒体管理器
        MediaManager.init()
        sensor.run()

        # 4. 硬件配置
        print("配置硬件...")
        fpioa = FPIOA()
        fpioa.set_function(5, FPIOA.UART2_TXD)
        fpioa.set_function(6, FPIOA.UART2_RXD)
        fpioa.set_function(53, FPIOA.GPIO53)

        key = Pin(53, Pin.IN, Pin.PULL_DOWN)
        uart2 = UART(UART.UART2, 115200)
        clock = time.clock()

        # 5. 触摸屏初始化
        tp = TOUCH(0)
        touch_counter = 0

        # 6. PID控制器
        class SimplePID:
            def __init__(self, kp=0.5, ki=0.1, kd=0.2):
                self.kp = kp
                self.ki = ki
                self.kd = kd
                self.prev_error = 0
                self.integral = 0
                self.prev_time = ticks_ms()

            def compute(self, error):
                current_time = ticks_ms()
                dt = (current_time - self.prev_time) / 1000.0
                if dt <= 0: dt = 0.01

                self.integral += error * dt
                if self.integral > 50: self.integral = 50
                elif self.integral < -50: self.integral = -50

                derivative = (error - self.prev_error) / dt
                output = self.kp * error + self.ki * self.integral + self.kd * derivative

                if output > 50: output = 50
                elif output < -50: output = -50

                self.prev_error = error
                self.prev_time = current_time
                return output

        pid_x = SimplePID(kp=0.5, ki=0.1, kd=0.2)
        pid_y = SimplePID(kp=0.5, ki=0.1, kd=0.2)

        # 7. 发送指令函数
        def send_gimbal_command(pan_cmd, tilt_cmd, rect_x=0, rect_y=0):
            try:
                pan_cmd = float(pan_cmd) if pan_cmd is not None else 0.0
                tilt_cmd = float(tilt_cmd) if tilt_cmd is not None else 0.0
                rect_x = int(rect_x) if rect_x is not None else 0
                rect_y = int(rect_y) if rect_y is not None else 0

                pan_int = int(pan_cmd * 10) + 1000
                tilt_int = int(tilt_cmd * 10) + 1000

                rx_order = [0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE]
                rx_order[1] = (pan_int >> 8) & 0xFF
                rx_order[2] = pan_int & 0xFF
                rx_order[3] = (tilt_int >> 8) & 0xFF
                rx_order[4] = tilt_int & 0xFF
                rx_order[5] = (rect_x >> 8) & 0xFF
                rx_order[6] = rect_x & 0xFF
                rx_order[7] = (rect_y >> 8) & 0xFF
                rx_order[8] = rect_y & 0xFF

                uart2.write(bytes(rx_order))
                print(f"云台指令: Pan={pan_cmd:.2f}, Tilt={tilt_cmd:.2f}")
            except Exception as e:
                print(f"发送指令失败: {e}")

        # 8. 显示图像到屏幕函数
        def show_img_to_screen(img):
            try:
                if img.height() > 480 or img.width() > 800:
                    scale = max(img.height() // 480, img.width() // 800) + 1
                    img.midpoint_pool(scale, scale)
                Display.show_image(img, x=(800-img.width())//2, y=(480-img.height())//2)
            except Exception as e:
                print(f"显示图像失败: {e}")

        # 9. 阈值调整功能
        def threshold_adjust_mode(threshold_dict):
            print("进入阈值调整模式")

            # 清空当前的矩形阈值
            threshold_dict['rect'] = []

            button_color = (150, 150, 150)
            text_color = (0, 0, 0)

            # 创建按钮界面
            ui_img = image.Image(800, 480, image.RGB565)
            ui_img.draw_rectangle(0, 0, 800, 480, color=(255, 255, 255), thickness=2, fill=True)

            # 按钮--返回
            ui_img.draw_rectangle(0, 0, 160, 40, color=button_color, thickness=2, fill=True)
            ui_img.draw_string_advanced(50, 0, 30, "返回", color=text_color)

            # 按钮--归位
            ui_img.draw_rectangle(0, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
            ui_img.draw_string_advanced(50, 480-40, 30, "归位", color=text_color)

            # 按钮--保存
            ui_img.draw_rectangle(800-160, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
            ui_img.draw_string_advanced(800-110, 480-40, 30, "保存", color=text_color)

            # 绘制滑块控制按钮 (只需要2个，控制矩形阈值的上下限)
            for j in [0, 800 - 160]:
                for i in range(60, 180, 60):  # 只需要2行
                    ui_img.draw_rectangle(j, i, 160, 40, color=button_color, thickness=2, fill=True)

            # 添加标签
            ui_img.draw_string_advanced(10, 70, 25, "下限-", color=text_color)
            ui_img.draw_string_advanced(650, 70, 25, "下限+", color=text_color)
            ui_img.draw_string_advanced(10, 130, 25, "上限-", color=text_color)
            ui_img.draw_string_advanced(650, 130, 25, "上限+", color=text_color)

            def which_button(x, y):
                if x < 160:
                    if y < 40:
                        return "return"
                    if y > 480 - 40:
                        return "reset"
                    if 60 <= y < 100:
                        return "0"  # 下限-
                    if 120 <= y < 160:
                        return "1"  # 上限-
                elif x > 800-160:
                    if y > 480 - 40:
                        return "save"
                    if 60 <= y < 100:
                        return "2"  # 下限+
                    if 120 <= y < 160:
                        return "3"  # 上限+
                return None

            # 矩形阈值 [下限, 上限]
            threshold_current = [59, 246]
            cut_roi = (540, 300, 480, 480)

            while True:
                try:
                    # 获取预览图像
                    preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)
                    preview_img = preview_img.copy(roi=cut_roi)

                    # 应用当前阈值
                    preview_gray = preview_img.to_grayscale()
                    preview_binary = preview_gray.binary([threshold_current])
                    preview_rgb = preview_binary.to_rgb565()

                    # 将预览图像绘制到UI上
                    ui_img.draw_image(preview_rgb, (800-preview_rgb.width()) // 2, (480-preview_rgb.height()) // 2)

                    # 显示当前阈值
                    ui_img.draw_rectangle(200, 200, 400, 80, color=(255, 255, 255), thickness=2, fill=True)
                    ui_img.draw_string_advanced(220, 210, 30, f"矩形阈值: [{threshold_current[0]}, {threshold_current[1]}]", color=(0, 0, 0))

                    # 处理触摸
                    points = tp.read()
                    if len(points) > 0:
                        button = which_button(points[0].x, points[0].y)
                        if button:
                            if button == "return":
                                print("退出阈值调整模式")
                                return False
                            elif button == "reset":
                                threshold_current = [0, 255]
                                print("阈值已重置")
                            elif button == "save":
                                threshold_dict['rect'].append(threshold_current.copy())
                                print(f"保存阈值: {threshold_current}")
                                ui_img.draw_rectangle(200, 300, 400, 40, color=(0, 255, 0), thickness=2, fill=True)
                                ui_img.draw_string_advanced(300, 310, 30, "保存成功!", color=(0, 0, 0))
                            elif button == "0":  # 下限-
                                threshold_current[0] = max(0, threshold_current[0] - 2)
                            elif button == "1":  # 上限-
                                threshold_current[1] = max(threshold_current[0], threshold_current[1] - 2)
                            elif button == "2":  # 下限+
                                threshold_current[0] = min(threshold_current[1], threshold_current[0] + 2)
                            elif button == "3":  # 上限+
                                threshold_current[1] = min(255, threshold_current[1] + 2)

                            time.sleep_ms(100)  # 防止连续触发

                    show_img_to_screen(ui_img)

                except Exception as e:
                    print(f"阈值调整模式错误: {e}")
                    continue

        # 10. 系统参数
        img_width = 480
        img_height = 480
        center_x = img_width // 2
        center_y = img_height // 2
        cut_roi = (540, 300, 480, 480)

        target_locked = False
        lost_count = 0
        max_lost_frames = 20
        frame_count = 0

        # 系统状态: 0=待机, 1=跟踪, 2=阈值调整
        system_mode = 0

        # 阈值字典 - 初始值
        threshold_dict = {'rect': [(59, 246)]}

        print("开始主循环...")
        print("长按屏幕进入阈值调整模式")
        print("按键开始矩形跟踪")

        # 11. 主循环
        while running:
            try:
                clock.tick()

                # 获取图像
                img = sensor.snapshot(chn=CAM_CHN_ID_0)
                if img is None:
                    print("获取图像失败，继续尝试...")
                    time.sleep_ms(10)
                    continue

                img = img.copy(roi=cut_roi)
                frame_count += 1

                # 处理触摸 - 长按进入阈值调整模式
                points = tp.read()
                if len(points) > 0:
                    touch_counter += 1
                    if touch_counter > 30 and system_mode == 0:  # 长按进入阈值调整
                        system_mode = 2
                        threshold_adjust_mode(threshold_dict)
                        system_mode = 0
                        touch_counter = 0
                else:
                    touch_counter = max(0, touch_counter - 2)

                # 按键处理 - 开始跟踪
                if key.value() == 1 and system_mode == 0:
                    print("开始矩形跟踪模式")
                    system_mode = 1
                    time.sleep_ms(500)

                # 跟踪模式
                if system_mode == 1:
                    rect_center = None
                    pan_cmd = 0.0
                    tilt_cmd = 0.0
                    error_x = 0.0
                    error_y = 0.0

                    try:
                        # 矩形检测
                        img_rect = img.to_grayscale(copy=True)
                        img_rect = img_rect.binary(threshold_dict['rect'])
                        rects = img_rect.find_rects(threshold=10000)

                        if rects and len(rects) >= 1:
                            best_rect = max(rects, key=lambda r: r.w() * r.h())
                            corner = best_rect.corners()

                            # 绘制矩形边框
                            img.draw_line(corner[0][0], corner[0][1], corner[1][0], corner[1][1], color=(0, 255, 0), thickness=3)
                            img.draw_line(corner[2][0], corner[2][1], corner[1][0], corner[1][1], color=(0, 255, 0), thickness=3)
                            img.draw_line(corner[2][0], corner[2][1], corner[3][0], corner[3][1], color=(0, 255, 0), thickness=3)
                            img.draw_line(corner[0][0], corner[0][1], corner[3][0], corner[3][1], color=(0, 255, 0), thickness=3)

                            # 计算矩形中心
                            c_x = sum([corner[k][0] for k in range(4)]) / 4
                            c_y = sum([corner[k][1] for k in range(4)]) / 4
                            rect_center = (c_x, c_y)

                            # 绘制中心点
                            img.draw_circle(int(c_x), int(c_y), 8, color=(255, 0, 0), thickness=3, fill=True)
                            img.draw_line(center_x, center_y, int(c_x), int(c_y), color=(255, 0, 0), thickness=2)

                            target_locked = True
                            lost_count = 0

                            # 计算偏差和PID控制
                            error_x = c_x - center_x
                            error_y = c_y - center_y
                            pan_cmd = pid_x.compute(error_x)
                            tilt_cmd = pid_y.compute(error_y)

                            # 发送控制指令
                            send_gimbal_command(pan_cmd, tilt_cmd, int(c_x), int(c_y))

                        else:
                            target_locked = False
                            lost_count += 1
                            if lost_count > max_lost_frames:
                                send_gimbal_command(0, 0)
                                system_mode = 0  # 返回待机模式
                                print("目标丢失，返回待机模式")

                    except Exception as e:
                        print(f"矩形检测错误: {e}")

                # 绘制界面信息
                try:
                    # 中心十字线
                    img.draw_line(center_x - 20, center_y, center_x + 20, center_y, color=(255, 255, 255), thickness=2)
                    img.draw_line(center_x, center_y - 20, center_x, center_y + 20, color=(255, 255, 255), thickness=2)

                    # 状态信息
                    if system_mode == 0:
                        status_text = "STANDBY"
                        status_color = (255, 255, 0)
                    elif system_mode == 1:
                        status_text = "TRACKING" if target_locked else "SEARCHING"
                        status_color = (0, 255, 0) if target_locked else (255, 255, 0)
                    else:
                        status_text = "ADJUSTING"
                        status_color = (255, 0, 255)

                    img.draw_string_advanced(10, 10, 30, f"Mode: {status_text}", color=status_color)
                    img.draw_string_advanced(10, 40, 25, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
                    img.draw_string_advanced(10, 65, 25, f"Frame: {frame_count}", color=(255, 255, 255))

                    if system_mode == 1 and rect_center:
                        img.draw_string_advanced(10, 90, 25, f"Target: ({int(rect_center[0])}, {int(rect_center[1])})", color=(0, 255, 0))
                        img.draw_string_advanced(10, 115, 25, f"Error: ({error_x:.1f}, {error_y:.1f})", color=(255, 255, 255))
                        img.draw_string_advanced(10, 140, 25, f"Gimbal: Pan={pan_cmd:.1f}, Tilt={tilt_cmd:.1f}", color=(255, 0, 255))

                    # 操作提示
                    if system_mode == 0:
                        img.draw_string_advanced(10, 400, 25, "长按屏幕: 调阈值", color=(255, 255, 255))
                        img.draw_string_advanced(10, 425, 25, "按键: 开始跟踪", color=(255, 255, 255))
                        img.draw_string_advanced(10, 450, 25, f"当前阈值: {threshold_dict['rect']}", color=(0, 255, 255))

                except Exception as e:
                    print(f"绘制界面错误: {e}")

                # 显示图像
                show_img_to_screen(img)

                # 定期状态报告
                if frame_count % 1000 == 0:
                    print(f"系统运行中... 模式: {system_mode}, 帧数: {frame_count}, FPS: {clock.fps():.1f}")

                time.sleep_ms(1)

            except KeyboardInterrupt:
                print("检测到键盘中断")
                running = False
                break
            except Exception as e:
                print(f"主循环错误: {e}")
                print("遇到错误但继续运行...")
                running = False
                time.sleep_ms(100)
                continue

        print("主循环已退出")

    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"系统严重错误: {e}")
        print("程序即将退出")
    finally:
        cleanup()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("程序中断")
    except Exception as e:
        print(f"未处理的异常: {e}")
    finally:
        cleanup()
        print("程序退出")
