# K230矩形跟踪系统 - 优化版本
# 功能：矩形跟踪 + 脱机调矩形阈值 + 多角度稳定识别
# 按键进入阈值调整模式，默认一直处于识别矩形模式
# 阈值掉电不丢失，多角度识别优化

import time
import os
import json

# 导入模块
from media.sensor import *
from media.display import *
from media.media import *
from machine import FPIOA, Pin, UART, TOUCH
from time import ticks_ms
import image
import math

print("模块导入成功")

# 全局变量
sensor = None
running = True

# 阈值配置文件路径
THRESHOLD_CONFIG_FILE = "/root/threshold_config.json"

def load_threshold_config():
    """加载阈值配置"""
    try:
        if os.path.exists(THRESHOLD_CONFIG_FILE):
            with open(THRESHOLD_CONFIG_FILE, 'r') as f:
                config = json.load(f)
                # 确保阈值是元组格式
                if 'rect' in config:
                    config['rect'] = [tuple(t) if isinstance(t, list) else t for t in config['rect']]
                print(f"加载阈值配置: {config}")
                return config
        else:
            # 默认配置
            default_config = {'rect': [(59, 246)]}
            save_threshold_config(default_config)
            return default_config
    except Exception as e:
        print(f"加载阈值配置失败: {e}")
        return {'rect': [(59, 246)]}

def save_threshold_config(threshold_dict):
    """保存阈值配置"""
    try:
        with open(THRESHOLD_CONFIG_FILE, 'w') as f:
            json.dump(threshold_dict, f)
        print(f"保存阈值配置: {threshold_dict}")
    except Exception as e:
        print(f"保存阈值配置失败: {e}")

def cleanup():
    global sensor, running
    print("开始清理...")
    running = False
    try:
        if sensor:
            sensor.stop()
            print("摄像头已停止")
        Display.deinit()
        print("显示系统已关闭")
        MediaManager.deinit()
        print("媒体管理器已关闭")
    except Exception as e:
        print(f"清理过程中出错: {e}")
    print("清理完成")

def main():
    global sensor, running

    try:
        print("=== K230矩形跟踪系统 (集成调阈值功能) ===")

        # 1. 摄像头初始化
        print("初始化摄像头...")
        sensor = Sensor(width=1920, height=1080)
        sensor.reset()
        sensor.set_framesize(width=1920, height=1080)
        sensor.set_pixformat(Sensor.RGB565)

        # 2. 显示初始化
        print("初始化显示...")
        try:
            Display.init(Display.ST7701, to_ide=False, width=800, height=480)  # 使用LCD屏幕
            print("LCD显示初始化完成")
        except:
            # 备用显示方式
            Display.init(Display.LT9611, to_ide=False)
            print("备用显示初始化完成")

        # 3. 媒体管理器
        MediaManager.init()
        sensor.run()

        # 4. 硬件配置
        print("配置硬件...")
        fpioa = FPIOA()
        fpioa.set_function(5, FPIOA.UART2_TXD)
        fpioa.set_function(6, FPIOA.UART2_RXD)
        fpioa.set_function(53, FPIOA.GPIO53)

        key = Pin(53, Pin.IN, Pin.PULL_DOWN)
        uart2 = UART(UART.UART2, 115200)
        clock = time.clock()

        # 5. 触摸屏初始化
        tp = TOUCH(0)
        touch_counter = 0

        # 6. 优化的矩形识别算法
        class AdvancedRectDetector:
            def __init__(self):
                self.history_centers = []  # 历史中心点
                self.history_size = 5      # 保存历史帧数
                self.confidence_threshold = 0.6  # 置信度阈值

            def calculate_rect_confidence(self, rect, img_width, img_height):
                """计算矩形的置信度分数"""
                area = rect.w() * rect.h()
                aspect_ratio = rect.w() / rect.h() if rect.h() > 0 else 0

                # 面积分数 (适中的面积更好)
                area_score = min(area / (img_width * img_height * 0.1), 1.0)
                if area < 1000:  # 太小的矩形降低分数
                    area_score *= 0.5

                # 长宽比分数 (接近正方形或常见矩形比例更好)
                if 0.5 <= aspect_ratio <= 2.0:
                    ratio_score = 1.0
                elif 0.3 <= aspect_ratio <= 3.0:
                    ratio_score = 0.8
                else:
                    ratio_score = 0.4

                # 位置分数 (中心附近的矩形更好)
                center_x = rect.x() + rect.w() / 2
                center_y = rect.y() + rect.h() / 2
                distance_from_center = math.sqrt((center_x - img_width/2)**2 + (center_y - img_height/2)**2)
                max_distance = math.sqrt((img_width/2)**2 + (img_height/2)**2)
                position_score = 1.0 - (distance_from_center / max_distance)

                # 综合分数
                confidence = (area_score * 0.4 + ratio_score * 0.4 + position_score * 0.2)
                return confidence

            def smooth_center(self, new_center):
                """平滑中心点，减少抖动"""
                self.history_centers.append(new_center)
                if len(self.history_centers) > self.history_size:
                    self.history_centers.pop(0)

                # 加权平均，最新的点权重更大
                weights = [i + 1 for i in range(len(self.history_centers))]
                total_weight = sum(weights)

                smooth_x = sum(center[0] * weight for center, weight in zip(self.history_centers, weights)) / total_weight
                smooth_y = sum(center[1] * weight for center, weight in zip(self.history_centers, weights)) / total_weight

                return (smooth_x, smooth_y)

            def detect_best_rect(self, rects, img_width, img_height):
                """检测最佳矩形"""
                if not rects:
                    return None, 0

                best_rect = None
                best_confidence = 0

                for rect in rects:
                    confidence = self.calculate_rect_confidence(rect, img_width, img_height)
                    if confidence > best_confidence:
                        best_confidence = confidence
                        best_rect = rect

                return best_rect, best_confidence

        # 7. PID控制器
        class SimplePID:
            def __init__(self, kp=0.5, ki=0.1, kd=0.2):
                self.kp = kp
                self.ki = ki
                self.kd = kd
                self.prev_error = 0
                self.integral = 0
                self.prev_time = ticks_ms()

            def compute(self, error):
                current_time = ticks_ms()
                dt = (current_time - self.prev_time) / 1000.0
                if dt <= 0: dt = 0.01

                self.integral += error * dt
                if self.integral > 50: self.integral = 50
                elif self.integral < -50: self.integral = -50

                derivative = (error - self.prev_error) / dt
                output = self.kp * error + self.ki * self.integral + self.kd * derivative

                if output > 50: output = 50
                elif output < -50: output = -50

                self.prev_error = error
                self.prev_time = current_time
                return output

        rect_detector = AdvancedRectDetector()
        pid_x = SimplePID(kp=0.5, ki=0.1, kd=0.2)
        pid_y = SimplePID(kp=0.5, ki=0.1, kd=0.2)

        # 7. 发送指令函数
        def send_gimbal_command(pan_cmd, tilt_cmd, rect_x=0, rect_y=0):
            try:
                pan_cmd = float(pan_cmd) if pan_cmd is not None else 0.0
                tilt_cmd = float(tilt_cmd) if tilt_cmd is not None else 0.0
                rect_x = int(rect_x) if rect_x is not None else 0
                rect_y = int(rect_y) if rect_y is not None else 0

                pan_int = int(pan_cmd * 10) + 1000
                tilt_int = int(tilt_cmd * 10) + 1000

                rx_order = [0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE]
                rx_order[1] = (pan_int >> 8) & 0xFF
                rx_order[2] = pan_int & 0xFF
                rx_order[3] = (tilt_int >> 8) & 0xFF
                rx_order[4] = tilt_int & 0xFF
                rx_order[5] = (rect_x >> 8) & 0xFF
                rx_order[6] = rect_x & 0xFF
                rx_order[7] = (rect_y >> 8) & 0xFF
                rx_order[8] = rect_y & 0xFF

                uart2.write(bytes(rx_order))
                print(f"云台指令: Pan={pan_cmd:.2f}, Tilt={tilt_cmd:.2f}")
            except Exception as e:
                print(f"发送指令失败: {e}")

        # 8. 显示图像到屏幕函数
        def show_img_to_screen(img):
            try:
                if img.height() > 480 or img.width() > 800:
                    scale = max(img.height() // 480, img.width() // 800) + 1
                    img.midpoint_pool(scale, scale)
                Display.show_image(img, x=(800-img.width())//2, y=(480-img.height())//2)
            except Exception as e:
                print(f"显示图像失败: {e}")

        # 9. 阈值调整功能
        def threshold_adjust_mode(threshold_dict):
            print("进入阈值调整模式")

            button_color = (150, 150, 150)
            text_color = (0, 0, 0)

            # 创建按钮界面
            ui_img = image.Image(800, 480, image.RGB565)
            ui_img.draw_rectangle(0, 0, 800, 480, color=(255, 255, 255), thickness=2, fill=True)

            # 按钮--返回
            ui_img.draw_rectangle(0, 0, 160, 40, color=button_color, thickness=2, fill=True)
            ui_img.draw_string_advanced(50, 0, 30, "返回", color=text_color)

            # 按钮--归位
            ui_img.draw_rectangle(0, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
            ui_img.draw_string_advanced(50, 480-40, 30, "归位", color=text_color)

            # 按钮--保存
            ui_img.draw_rectangle(800-160, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
            ui_img.draw_string_advanced(800-110, 480-40, 30, "保存", color=text_color)

            # 绘制滑块控制按钮 (只需要2个，控制矩形阈值的上下限)
            for j in [0, 800 - 160]:
                for i in range(60, 180, 60):  # 只需要2行
                    ui_img.draw_rectangle(j, i, 160, 40, color=button_color, thickness=2, fill=True)

            # 添加标签
            ui_img.draw_string_advanced(10, 70, 25, "下限-", color=text_color)
            ui_img.draw_string_advanced(650, 70, 25, "下限+", color=text_color)
            ui_img.draw_string_advanced(10, 130, 25, "上限-", color=text_color)
            ui_img.draw_string_advanced(650, 130, 25, "上限+", color=text_color)

            def which_button(x, y):
                if x < 160:
                    if y < 40:
                        return "return"
                    if y > 480 - 40:
                        return "reset"
                    if 60 <= y < 100:
                        return "0"  # 下限-
                    if 120 <= y < 160:
                        return "1"  # 上限-
                elif x > 800-160:
                    if y > 480 - 40:
                        return "save"
                    if 60 <= y < 100:
                        return "2"  # 下限+
                    if 120 <= y < 160:
                        return "3"  # 上限+
                return None

            # 矩形阈值 [下限, 上限]
            threshold_current = [59, 246]
            cut_roi = (540, 300, 480, 480)

            while True:
                try:
                    # 获取预览图像
                    preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)
                    preview_img = preview_img.copy(roi=cut_roi)

                    # 应用当前阈值
                    preview_gray = preview_img.to_grayscale()
                    preview_binary = preview_gray.binary([threshold_current])
                    preview_rgb = preview_binary.to_rgb565()

                    # 将预览图像绘制到UI上
                    ui_img.draw_image(preview_rgb, (800-preview_rgb.width()) // 2, (480-preview_rgb.height()) // 2)

                    # 显示当前阈值
                    ui_img.draw_rectangle(200, 200, 400, 80, color=(255, 255, 255), thickness=2, fill=True)
                    ui_img.draw_string_advanced(220, 210, 30, f"矩形阈值: [{threshold_current[0]}, {threshold_current[1]}]", color=(0, 0, 0))

                    # 处理触摸
                    points = tp.read()
                    if len(points) > 0:
                        button = which_button(points[0].x, points[0].y)
                        if button:
                            if button == "return":
                                print("退出阈值调整模式")
                                return False
                            elif button == "reset":
                                threshold_current = [0, 255]
                                print("阈值已重置")
                            elif button == "save":
                                threshold_dict['rect'] = [threshold_current.copy()]
                                save_threshold_config(threshold_dict)  # 保存到文件
                                print(f"保存阈值: {threshold_current}")
                                ui_img.draw_rectangle(200, 300, 400, 40, color=(0, 255, 0), thickness=2, fill=True)
                                ui_img.draw_string_advanced(300, 310, 30, "保存成功!", color=(0, 0, 0))
                            elif button == "0":  # 下限-
                                threshold_current[0] = max(0, threshold_current[0] - 2)
                            elif button == "1":  # 上限-
                                threshold_current[1] = max(threshold_current[0], threshold_current[1] - 2)
                            elif button == "2":  # 下限+
                                threshold_current[0] = min(threshold_current[1], threshold_current[0] + 2)
                            elif button == "3":  # 上限+
                                threshold_current[1] = min(255, threshold_current[1] + 2)

                            time.sleep_ms(100)  # 防止连续触发

                    show_img_to_screen(ui_img)

                except Exception as e:
                    print(f"阈值调整模式错误: {e}")
                    continue

        # 10. 系统参数
        CAM_CHN_ID_0 = 0  # 摄像头通道ID
        img_width = 480
        img_height = 480
        center_x = img_width // 2
        center_y = img_height // 2
        cut_roi = (540, 300, 480, 480)

        target_locked = False
        lost_count = 0
        max_lost_frames = 20
        frame_count = 0

        # 系统状态: 1=跟踪模式(默认), 2=阈值调整模式
        system_mode = 1

        # 加载保存的阈值配置
        threshold_dict = load_threshold_config()

        print("开始主循环...")
        print("按键进入阈值调整模式")
        print("默认处于矩形跟踪模式")

        # 11. 主循环
        while running:
            try:
                clock.tick()

                # 获取图像
                img = sensor.snapshot(chn=CAM_CHN_ID_0)
                if img is None:
                    print("获取图像失败，继续尝试...")
                    time.sleep_ms(10)
                    continue

                img = img.copy(roi=cut_roi)
                frame_count += 1

                # 按键处理 - 进入阈值调整模式
                if key.value() == 1:
                    if system_mode == 1:  # 从跟踪模式进入阈值调整模式
                        print("进入阈值调整模式")
                        system_mode = 2
                        threshold_adjust_mode(threshold_dict)
                        system_mode = 1  # 调整完成后返回跟踪模式
                        time.sleep_ms(500)  # 防止按键重复触发

                # 跟踪模式
                if system_mode == 1:
                    rect_center = None
                    pan_cmd = 0.0
                    tilt_cmd = 0.0
                    error_x = 0.0
                    error_y = 0.0
                    confidence = 0.0

                    try:
                        # 优化的矩形检测
                        img_rect = img.to_grayscale(copy=True)

                        # 多阈值检测，提高不同角度的识别稳定性
                        all_rects = []
                        for threshold in threshold_dict['rect']:
                            try:
                                binary_img = img_rect.binary([threshold])
                                # 形态学操作，减少噪声和提高稳定性
                                binary_img.erode(1)
                                binary_img.dilate(3)  # 增强连接
                                binary_img.erode(2)   # 去除小噪声

                                # 多尺度检测
                                rects1 = binary_img.find_rects(threshold=3000)   # 小矩形
                                rects2 = binary_img.find_rects(threshold=8000)   # 中等矩形
                                rects3 = binary_img.find_rects(threshold=15000)  # 大矩形

                                all_rects.extend(rects1)
                                all_rects.extend(rects2)
                                all_rects.extend(rects3)
                            except Exception as e:
                                print(f"阈值 {threshold} 检测失败: {e}")
                                continue

                        if all_rects:
                            # 使用优化算法选择最佳矩形
                            best_rect, confidence = rect_detector.detect_best_rect(all_rects, img_width, img_height)

                            if best_rect and confidence > rect_detector.confidence_threshold:
                                corner = best_rect.corners()

                                # 绘制矩形边框，颜色根据置信度变化
                                if confidence > 0.8:
                                    rect_color = (0, 255, 0)  # 绿色 - 高置信度
                                elif confidence > 0.7:
                                    rect_color = (255, 255, 0)  # 黄色 - 中等置信度
                                else:
                                    rect_color = (255, 128, 0)  # 橙色 - 低置信度

                                # 绘制矩形边框
                                img.draw_line(corner[0][0], corner[0][1], corner[1][0], corner[1][1], color=rect_color, thickness=3)
                                img.draw_line(corner[2][0], corner[2][1], corner[1][0], corner[1][1], color=rect_color, thickness=3)
                                img.draw_line(corner[2][0], corner[2][1], corner[3][0], corner[3][1], color=rect_color, thickness=3)
                                img.draw_line(corner[0][0], corner[0][1], corner[3][0], corner[3][1], color=rect_color, thickness=3)

                                # 计算矩形中心
                                c_x = sum([corner[k][0] for k in range(4)]) / 4
                                c_y = sum([corner[k][1] for k in range(4)]) / 4

                                # 使用平滑算法减少抖动
                                smooth_center = rect_detector.smooth_center((c_x, c_y))
                                rect_center = smooth_center

                                # 绘制中心点
                                img.draw_circle(int(smooth_center[0]), int(smooth_center[1]), 8, color=(255, 0, 0), thickness=3, fill=True)
                                img.draw_line(center_x, center_y, int(smooth_center[0]), int(smooth_center[1]), color=(255, 0, 0), thickness=2)

                                target_locked = True
                                lost_count = 0

                                # 计算偏差和PID控制
                                error_x = smooth_center[0] - center_x
                                error_y = smooth_center[1] - center_y
                                pan_cmd = pid_x.compute(error_x)
                                tilt_cmd = pid_y.compute(error_y)

                                # 发送控制指令
                                send_gimbal_command(pan_cmd, tilt_cmd, int(smooth_center[0]), int(smooth_center[1]))

                            else:
                                target_locked = False
                                lost_count += 1
                        else:
                            target_locked = False
                            lost_count += 1

                        # 目标丢失处理
                        if lost_count > max_lost_frames:
                            send_gimbal_command(0, 0)
                            print("目标丢失，继续搜索...")
                            # 不切换模式，继续在跟踪模式中搜索

                    except Exception as e:
                        print(f"矩形检测错误: {e}")

                # 绘制界面信息
                try:
                    # 中心十字线
                    img.draw_line(center_x - 20, center_y, center_x + 20, center_y, color=(255, 255, 255), thickness=2)
                    img.draw_line(center_x, center_y - 20, center_x, center_y + 20, color=(255, 255, 255), thickness=2)

                    # 状态信息
                    if system_mode == 1:
                        status_text = "TRACKING" if target_locked else "SEARCHING"
                        status_color = (0, 255, 0) if target_locked else (255, 255, 0)
                    else:
                        status_text = "ADJUSTING"
                        status_color = (255, 0, 255)

                    img.draw_string_advanced(10, 10, 30, f"Mode: {status_text}", color=status_color)
                    img.draw_string_advanced(10, 40, 25, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
                    img.draw_string_advanced(10, 65, 25, f"Frame: {frame_count}", color=(255, 255, 255))

                    if system_mode == 1:
                        if rect_center:
                            img.draw_string_advanced(10, 90, 25, f"Target: ({int(rect_center[0])}, {int(rect_center[1])})", color=(0, 255, 0))
                            img.draw_string_advanced(10, 115, 25, f"Error: ({error_x:.1f}, {error_y:.1f})", color=(255, 255, 255))
                            img.draw_string_advanced(10, 140, 25, f"Gimbal: Pan={pan_cmd:.1f}, Tilt={tilt_cmd:.1f}", color=(255, 0, 255))
                            img.draw_string_advanced(10, 165, 25, f"Confidence: {confidence:.2f}", color=(0, 255, 255))

                        # 操作提示
                        img.draw_string_advanced(10, 400, 25, "按键: 调阈值", color=(255, 255, 255))
                        img.draw_string_advanced(10, 425, 25, f"当前阈值: {threshold_dict['rect']}", color=(0, 255, 255))
                        img.draw_string_advanced(10, 450, 25, f"丢失计数: {lost_count}/{max_lost_frames}", color=(255, 255, 0))

                except Exception as e:
                    print(f"绘制界面错误: {e}")

                # 显示图像
                show_img_to_screen(img)

                # 定期状态报告
                if frame_count % 1000 == 0:
                    print(f"系统运行中... 模式: {system_mode}, 帧数: {frame_count}, FPS: {clock.fps():.1f}")

                time.sleep_ms(1)

            except KeyboardInterrupt:
                print("检测到键盘中断")
                running = False
                break
            except Exception as e:
                print(f"主循环错误: {e}")
                print("遇到错误但继续运行...")
                running = False
                time.sleep_ms(100)
                continue

        print("主循环已退出")

    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"系统严重错误: {e}")
        print("程序即将退出")
    finally:
        cleanup()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("程序中断")
    except Exception as e:
        print(f"未处理的异常: {e}")
    finally:
        cleanup()
        print("程序退出")
