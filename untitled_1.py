# K230矩形跟踪系统 - 优化版本
# 功能：矩形跟踪 + 脱机调矩形阈值 + 多角度稳定识别
# 按键进入阈值调整模式，默认一直处于识别矩形模式
# 阈值掉电不丢失，多角度识别优化

import time
import os
import json

# 导入模块
from media.sensor import *
from media.display import *
from media.media import *
from machine import FPIOA, Pin, UART, TOUCH
from time import ticks_ms
import image
import math

print("模块导入成功")

# 全局变量
sensor = None
running = True

# 阈值配置文件路径
THRESHOLD_CONFIG_FILE = "/root/threshold_config.json"

def load_threshold_config():
    """加载阈值配置"""
    try:
        if os.path.exists(THRESHOLD_CONFIG_FILE):
            with open(THRESHOLD_CONFIG_FILE, 'r') as f:
                config = json.load(f)
                # 确保阈值是元组格式
                if 'rect' in config:
                    config['rect'] = [tuple(t) if isinstance(t, list) else t for t in config['rect']]
                print(f"加载阈值配置: {config}")
                return config
        else:
            # 默认配置
            default_config = {'rect': [(59, 246)]}
            save_threshold_config(default_config)
            return default_config
    except Exception as e:
        print(f"加载阈值配置失败: {e}")
        return {'rect': [(59, 246)]}

def save_threshold_config(threshold_dict):
    """保存阈值配置"""
    try:
        with open(THRESHOLD_CONFIG_FILE, 'w') as f:
            json.dump(threshold_dict, f)
        print(f"保存阈值配置: {threshold_dict}")
    except Exception as e:
        print(f"保存阈值配置失败: {e}")

def cleanup():
    global sensor, running
    print("开始清理...")
    running = False
    try:
        if sensor:
            sensor.stop()
            print("摄像头已停止")
        Display.deinit()
        print("显示系统已关闭")
        MediaManager.deinit()
        print("媒体管理器已关闭")
    except Exception as e:
        print(f"清理过程中出错: {e}")
    print("清理完成")

def main():
    global sensor, running

    try:
        print("=== K230矩形跟踪系统 (集成调阈值功能) ===")

        # 1. 摄像头初始化
        print("初始化摄像头...")
        sensor = Sensor(width=1920, height=1080)
        sensor.reset()
        sensor.set_framesize(width=1920, height=1080)
        sensor.set_pixformat(Sensor.RGB565)

        # 2. 显示初始化
        print("初始化显示...")
        try:
            Display.init(Display.ST7701, to_ide=False, width=800, height=480)  # 使用LCD屏幕
            print("LCD显示初始化完成")
        except:
            # 备用显示方式
            Display.init(Display.LT9611, to_ide=False)
            print("备用显示初始化完成")

        # 3. 媒体管理器
        MediaManager.init()
        sensor.run()

        # 4. 硬件配置
        print("配置硬件...")
        fpioa = FPIOA()
        fpioa.set_function(5, FPIOA.UART2_TXD)
        fpioa.set_function(6, FPIOA.UART2_RXD)
        fpioa.set_function(53, FPIOA.GPIO53)

        key = Pin(53, Pin.IN, Pin.PULL_DOWN)
        uart2 = UART(UART.UART2, 115200)
        clock = time.clock()

        # 5. 触摸屏初始化
        tp = TOUCH(0)
        touch_counter = 0

        # 6. 高精度矩形识别算法
        class PrecisionRectDetector:
            def __init__(self):
                self.history_centers = []
                self.history_size = 8      # 增加历史帧数
                self.confidence_threshold = 0.65  # 提高置信度阈值
                self.target_template = None  # 目标模板
                self.template_size = (100, 100)  # 模板大小
                self.last_good_rect = None

            def calculate_rect_confidence(self, rect, img_width, img_height):
                """计算矩形的置信度分数"""
                area = rect.w() * rect.h()
                aspect_ratio = rect.w() / rect.h() if rect.h() > 0 else 0

                # 面积分数 - 更严格的面积要求
                ideal_area = img_width * img_height * 0.05  # 期望面积约为图像的5%
                area_diff = abs(area - ideal_area) / ideal_area
                area_score = max(0, 1.0 - area_diff)

                # 最小面积限制
                if area < 2000:
                    area_score *= 0.3
                elif area < 5000:
                    area_score *= 0.7

                # 长宽比分数 - 针对矩形标靶优化
                if 0.7 <= aspect_ratio <= 1.4:  # 接近正方形
                    ratio_score = 1.0
                elif 0.5 <= aspect_ratio <= 2.0:
                    ratio_score = 0.8
                else:
                    ratio_score = 0.3

                # 位置分数 - 考虑历史位置
                center_x = rect.x() + rect.w() / 2
                center_y = rect.y() + rect.h() / 2

                if self.last_good_rect:
                    # 如果有历史好的矩形，优先考虑附近的
                    last_center_x = self.last_good_rect.x() + self.last_good_rect.w() / 2
                    last_center_y = self.last_good_rect.y() + self.last_good_rect.h() / 2
                    distance_from_last = math.sqrt((center_x - last_center_x)**2 + (center_y - last_center_y)**2)
                    if distance_from_last < 50:  # 50像素内
                        position_score = 1.0
                    elif distance_from_last < 100:
                        position_score = 0.8
                    else:
                        position_score = 0.5
                else:
                    # 没有历史记录时，优先中心区域
                    distance_from_center = math.sqrt((center_x - img_width/2)**2 + (center_y - img_height/2)**2)
                    max_distance = math.sqrt((img_width/2)**2 + (img_height/2)**2)
                    position_score = 1.0 - (distance_from_center / max_distance) * 0.5

                # 形状规整度分数
                perimeter = 2 * (rect.w() + rect.h())
                shape_score = min(1.0, (4 * math.pi * area) / (perimeter * perimeter)) if perimeter > 0 else 0

                # 综合分数 - 调整权重
                confidence = (area_score * 0.3 + ratio_score * 0.3 + position_score * 0.25 + shape_score * 0.15)
                return confidence

            def smooth_center(self, new_center):
                """高精度中心点平滑"""
                self.history_centers.append(new_center)
                if len(self.history_centers) > self.history_size:
                    self.history_centers.pop(0)

                if len(self.history_centers) == 1:
                    return new_center

                # 使用指数加权移动平均
                alpha = 0.3  # 平滑因子
                smooth_x = new_center[0]
                smooth_y = new_center[1]

                for i in range(len(self.history_centers) - 2, -1, -1):
                    weight = alpha * (1 - alpha) ** (len(self.history_centers) - 1 - i)
                    smooth_x += self.history_centers[i][0] * weight
                    smooth_y += self.history_centers[i][1] * weight

                return (smooth_x, smooth_y)

            def detect_best_rect(self, rects, img_width, img_height):
                """检测最佳矩形"""
                if not rects:
                    return None, 0

                best_rect = None
                best_confidence = 0

                for rect in rects:
                    confidence = self.calculate_rect_confidence(rect, img_width, img_height)
                    if confidence > best_confidence and confidence > self.confidence_threshold:
                        best_confidence = confidence
                        best_rect = rect

                # 更新历史好矩形
                if best_rect and best_confidence > 0.7:
                    self.last_good_rect = best_rect

                return best_rect, best_confidence

            def reset_history(self):
                """重置历史记录"""
                self.history_centers = []
                self.last_good_rect = None

        # 7. 优化的PID控制器
        class AdaptivePID:
            def __init__(self, kp=0.3, ki=0.05, kd=0.15, max_output=20.0):
                self.kp = kp
                self.ki = ki
                self.kd = kd
                self.max_output = max_output
                self.prev_error = 0
                self.integral = 0
                self.prev_time = ticks_ms()
                self.error_history = []
                self.max_history = 10

            def reset(self):
                """重置PID状态"""
                self.prev_error = 0
                self.integral = 0
                self.error_history = []
                self.prev_time = ticks_ms()

            def compute(self, error, target_visible=True):
                current_time = ticks_ms()
                dt = (current_time - self.prev_time) / 1000.0
                if dt <= 0: dt = 0.01

                # 记录误差历史
                self.error_history.append(abs(error))
                if len(self.error_history) > self.max_history:
                    self.error_history.pop(0)

                # 自适应参数调整
                avg_error = sum(self.error_history) / len(self.error_history) if self.error_history else 0

                # 根据误差大小和目标可见性调整参数
                if not target_visible:
                    # 目标不可见时，停止积分，减小输出
                    kp_adj = self.kp * 0.1
                    ki_adj = 0
                    kd_adj = self.kd * 0.5
                    max_out = self.max_output * 0.3
                elif avg_error > 50:
                    # 大误差时，增强比例控制
                    kp_adj = self.kp * 1.2
                    ki_adj = self.ki * 0.5
                    kd_adj = self.kd * 1.1
                    max_out = self.max_output
                elif avg_error < 10:
                    # 小误差时，增强积分和微分
                    kp_adj = self.kp * 0.8
                    ki_adj = self.ki * 1.2
                    kd_adj = self.kd * 1.3
                    max_out = self.max_output * 0.7
                else:
                    # 正常情况
                    kp_adj = self.kp
                    ki_adj = self.ki
                    kd_adj = self.kd
                    max_out = self.max_output

                # PID计算
                if target_visible:
                    self.integral += error * dt
                    # 积分限幅
                    integral_limit = max_out / (ki_adj + 0.001)
                    if self.integral > integral_limit:
                        self.integral = integral_limit
                    elif self.integral < -integral_limit:
                        self.integral = -integral_limit

                derivative = (error - self.prev_error) / dt
                output = kp_adj * error + ki_adj * self.integral + kd_adj * derivative

                # 输出限幅
                if output > max_out:
                    output = max_out
                elif output < -max_out:
                    output = -max_out

                self.prev_error = error
                self.prev_time = current_time
                return output

        # 8. 目标跟踪管理器
        class TargetTracker:
            def __init__(self):
                self.target_lost_count = 0
                self.max_lost_frames = 15
                self.search_mode = False
                self.search_direction = 1  # 1为右，-1为左
                self.search_speed = 2.0
                self.last_known_position = None
                self.stable_count = 0
                self.min_stable_frames = 5

            def update_target_status(self, target_found, target_center=None):
                if target_found:
                    self.target_lost_count = 0
                    self.search_mode = False
                    self.last_known_position = target_center
                    self.stable_count += 1
                else:
                    self.target_lost_count += 1
                    self.stable_count = 0

                    if self.target_lost_count > self.max_lost_frames:
                        self.search_mode = True

            def get_search_command(self):
                """获取搜索指令"""
                if self.search_mode:
                    # 简单的左右搜索模式
                    if self.target_lost_count % 60 == 0:  # 每60帧改变方向
                        self.search_direction *= -1
                    return self.search_direction * self.search_speed, 0
                return 0, 0

            def is_target_stable(self):
                return self.stable_count >= self.min_stable_frames

        rect_detector = PrecisionRectDetector()
        pid_x = AdaptivePID(kp=0.25, ki=0.03, kd=0.12, max_output=15.0)  # 降低增益
        pid_y = AdaptivePID(kp=0.25, ki=0.03, kd=0.12, max_output=15.0)
        target_tracker = TargetTracker()

        # 9. 优化的云台控制函数
        def send_gimbal_command(pan_cmd, tilt_cmd, rect_x=0, rect_y=0, force_send=False):
            try:
                pan_cmd = float(pan_cmd) if pan_cmd is not None else 0.0
                tilt_cmd = float(tilt_cmd) if tilt_cmd is not None else 0.0
                rect_x = int(rect_x) if rect_x is not None else 0
                rect_y = int(rect_y) if rect_y is not None else 0

                # 限制指令幅度，防止过大的转动
                max_single_cmd = 8.0  # 单次最大指令幅度
                pan_cmd = max(-max_single_cmd, min(max_single_cmd, pan_cmd))
                tilt_cmd = max(-max_single_cmd, min(max_single_cmd, tilt_cmd))

                # 死区处理，避免小幅抖动
                dead_zone = 0.5
                if abs(pan_cmd) < dead_zone and not force_send:
                    pan_cmd = 0.0
                if abs(tilt_cmd) < dead_zone and not force_send:
                    tilt_cmd = 0.0

                pan_int = int(pan_cmd * 10) + 1000
                tilt_int = int(tilt_cmd * 10) + 1000

                rx_order = [0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE]
                rx_order[1] = (pan_int >> 8) & 0xFF
                rx_order[2] = pan_int & 0xFF
                rx_order[3] = (tilt_int >> 8) & 0xFF
                rx_order[4] = tilt_int & 0xFF
                rx_order[5] = (rect_x >> 8) & 0xFF
                rx_order[6] = rect_x & 0xFF
                rx_order[7] = (rect_y >> 8) & 0xFF
                rx_order[8] = rect_y & 0xFF

                uart2.write(bytes(rx_order))
                if abs(pan_cmd) > 0.1 or abs(tilt_cmd) > 0.1 or force_send:
                    print(f"云台指令: Pan={pan_cmd:.2f}, Tilt={tilt_cmd:.2f}")
            except Exception as e:
                print(f"发送指令失败: {e}")

        # 10. 云台归零函数
        def gimbal_reset():
            """云台归零，解决上电右转问题"""
            print("云台归零中...")
            for i in range(10):
                send_gimbal_command(0, 0, force_send=True)
                time.sleep_ms(50)
            print("云台归零完成")

        # 8. 显示图像到屏幕函数
        def show_img_to_screen(img):
            try:
                if img.height() > 480 or img.width() > 800:
                    scale = max(img.height() // 480, img.width() // 800) + 1
                    img.midpoint_pool(scale, scale)
                Display.show_image(img, x=(800-img.width())//2, y=(480-img.height())//2)
            except Exception as e:
                print(f"显示图像失败: {e}")

        # 9. 阈值调整功能
        def threshold_adjust_mode(threshold_dict):
            print("进入阈值调整模式")

            button_color = (150, 150, 150)
            text_color = (0, 0, 0)

            # 创建按钮界面
            ui_img = image.Image(800, 480, image.RGB565)
            ui_img.draw_rectangle(0, 0, 800, 480, color=(255, 255, 255), thickness=2, fill=True)

            # 按钮--返回
            ui_img.draw_rectangle(0, 0, 160, 40, color=button_color, thickness=2, fill=True)
            ui_img.draw_string_advanced(50, 0, 30, "返回", color=text_color)

            # 按钮--归位
            ui_img.draw_rectangle(0, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
            ui_img.draw_string_advanced(50, 480-40, 30, "归位", color=text_color)

            # 按钮--保存
            ui_img.draw_rectangle(800-160, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
            ui_img.draw_string_advanced(800-110, 480-40, 30, "保存", color=text_color)

            # 绘制滑块控制按钮 (只需要2个，控制矩形阈值的上下限)
            for j in [0, 800 - 160]:
                for i in range(60, 180, 60):  # 只需要2行
                    ui_img.draw_rectangle(j, i, 160, 40, color=button_color, thickness=2, fill=True)

            # 添加标签
            ui_img.draw_string_advanced(10, 70, 25, "下限-", color=text_color)
            ui_img.draw_string_advanced(650, 70, 25, "下限+", color=text_color)
            ui_img.draw_string_advanced(10, 130, 25, "上限-", color=text_color)
            ui_img.draw_string_advanced(650, 130, 25, "上限+", color=text_color)

            def which_button(x, y):
                if x < 160:
                    if y < 40:
                        return "return"
                    if y > 480 - 40:
                        return "reset"
                    if 60 <= y < 100:
                        return "0"  # 下限-
                    if 120 <= y < 160:
                        return "1"  # 上限-
                elif x > 800-160:
                    if y > 480 - 40:
                        return "save"
                    if 60 <= y < 100:
                        return "2"  # 下限+
                    if 120 <= y < 160:
                        return "3"  # 上限+
                return None

            # 矩形阈值 [下限, 上限]
            threshold_current = [59, 246]
            cut_roi = (540, 300, 480, 480)

            while True:
                try:
                    # 获取预览图像
                    preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)
                    preview_img = preview_img.copy(roi=cut_roi)

                    # 应用当前阈值
                    preview_gray = preview_img.to_grayscale()
                    preview_binary = preview_gray.binary([threshold_current])
                    preview_rgb = preview_binary.to_rgb565()

                    # 将预览图像绘制到UI上
                    ui_img.draw_image(preview_rgb, (800-preview_rgb.width()) // 2, (480-preview_rgb.height()) // 2)

                    # 显示当前阈值
                    ui_img.draw_rectangle(200, 200, 400, 80, color=(255, 255, 255), thickness=2, fill=True)
                    ui_img.draw_string_advanced(220, 210, 30, f"矩形阈值: [{threshold_current[0]}, {threshold_current[1]}]", color=(0, 0, 0))

                    # 处理触摸
                    points = tp.read()
                    if len(points) > 0:
                        button = which_button(points[0].x, points[0].y)
                        if button:
                            if button == "return":
                                print("退出阈值调整模式")
                                return False
                            elif button == "reset":
                                threshold_current = [0, 255]
                                print("阈值已重置")
                            elif button == "save":
                                threshold_dict['rect'] = [threshold_current.copy()]
                                save_threshold_config(threshold_dict)  # 保存到文件
                                print(f"保存阈值: {threshold_current}")
                                ui_img.draw_rectangle(200, 300, 400, 40, color=(0, 255, 0), thickness=2, fill=True)
                                ui_img.draw_string_advanced(300, 310, 30, "保存成功!", color=(0, 0, 0))
                            elif button == "0":  # 下限-
                                threshold_current[0] = max(0, threshold_current[0] - 2)
                            elif button == "1":  # 上限-
                                threshold_current[1] = max(threshold_current[0], threshold_current[1] - 2)
                            elif button == "2":  # 下限+
                                threshold_current[0] = min(threshold_current[1], threshold_current[0] + 2)
                            elif button == "3":  # 上限+
                                threshold_current[1] = min(255, threshold_current[1] + 2)

                            time.sleep_ms(100)  # 防止连续触发

                    show_img_to_screen(ui_img)

                except Exception as e:
                    print(f"阈值调整模式错误: {e}")
                    continue

        # 10. 系统参数
        CAM_CHN_ID_0 = 0  # 摄像头通道ID
        img_width = 480
        img_height = 480
        center_x = img_width // 2
        center_y = img_height // 2
        cut_roi = (540, 300, 480, 480)

        target_locked = False
        lost_count = 0
        max_lost_frames = 20
        frame_count = 0

        # 系统状态: 1=跟踪模式(默认), 2=阈值调整模式
        system_mode = 1

        # 加载保存的阈值配置
        threshold_dict = load_threshold_config()

        print("开始主循环...")
        print("按键进入阈值调整模式")
        print("默认处于矩形跟踪模式")

        # 系统初始化 - 云台归零
        print("正在初始化云台...")
        gimbal_reset()
        time.sleep_ms(1000)  # 等待云台稳定

        # 11. 主循环
        while running:
            try:
                clock.tick()

                # 获取图像
                img = sensor.snapshot(chn=CAM_CHN_ID_0)
                if img is None:
                    print("获取图像失败，继续尝试...")
                    time.sleep_ms(10)
                    continue

                img = img.copy(roi=cut_roi)
                frame_count += 1

                # 按键处理 - 进入阈值调整模式
                if key.value() == 1:
                    if system_mode == 1:  # 从跟踪模式进入阈值调整模式
                        print("进入阈值调整模式")
                        system_mode = 2
                        threshold_adjust_mode(threshold_dict)
                        system_mode = 1  # 调整完成后返回跟踪模式
                        time.sleep_ms(500)  # 防止按键重复触发

                # 跟踪模式
                if system_mode == 1:
                    rect_center = None
                    pan_cmd = 0.0
                    tilt_cmd = 0.0
                    error_x = 0.0
                    error_y = 0.0
                    confidence = 0.0
                    target_found = False

                    try:
                        # 高精度矩形检测
                        img_rect = img.to_grayscale(copy=True)

                        # 多阈值检测，提高识别稳定性
                        all_rects = []
                        for threshold in threshold_dict['rect']:
                            try:
                                binary_img = img_rect.binary([threshold])

                                # 优化的形态学操作
                                binary_img.erode(1)    # 去除小噪声
                                binary_img.dilate(4)   # 连接断开的部分
                                binary_img.erode(3)    # 恢复形状
                                binary_img.dilate(2)   # 最终平滑

                                # 精确的多尺度检测
                                rects1 = binary_img.find_rects(threshold=2000)   # 小目标
                                rects2 = binary_img.find_rects(threshold=6000)   # 中等目标
                                rects3 = binary_img.find_rects(threshold=12000)  # 大目标
                                rects4 = binary_img.find_rects(threshold=20000)  # 超大目标

                                all_rects.extend(rects1)
                                all_rects.extend(rects2)
                                all_rects.extend(rects3)
                                all_rects.extend(rects4)
                            except Exception as e:
                                print(f"阈值 {threshold} 检测失败: {e}")
                                continue

                        if all_rects:
                            # 使用高精度算法选择最佳矩形
                            best_rect, confidence = rect_detector.detect_best_rect(all_rects, img_width, img_height)

                            if best_rect and confidence > rect_detector.confidence_threshold:
                                target_found = True
                                corner = best_rect.corners()

                                # 根据置信度选择颜色
                                if confidence > 0.85:
                                    rect_color = (0, 255, 0)    # 绿色 - 优秀
                                elif confidence > 0.75:
                                    rect_color = (128, 255, 0)  # 黄绿 - 良好
                                elif confidence > 0.65:
                                    rect_color = (255, 255, 0)  # 黄色 - 一般
                                else:
                                    rect_color = (255, 128, 0)  # 橙色 - 较差

                                # 绘制矩形边框
                                for i in range(4):
                                    next_i = (i + 1) % 4
                                    img.draw_line(corner[i][0], corner[i][1], corner[next_i][0], corner[next_i][1],
                                                color=rect_color, thickness=3)

                                # 计算精确中心
                                c_x = sum([corner[k][0] for k in range(4)]) / 4
                                c_y = sum([corner[k][1] for k in range(4)]) / 4

                                # 高精度中心点平滑
                                smooth_center = rect_detector.smooth_center((c_x, c_y))
                                rect_center = smooth_center

                                # 绘制中心点和连线
                                img.draw_circle(int(smooth_center[0]), int(smooth_center[1]), 6, color=(255, 0, 0), thickness=2, fill=True)
                                img.draw_line(center_x, center_y, int(smooth_center[0]), int(smooth_center[1]),
                                            color=(255, 0, 0), thickness=2)

                                # 计算偏差
                                error_x = smooth_center[0] - center_x
                                error_y = smooth_center[1] - center_y

                                # 自适应PID控制
                                pan_cmd = pid_x.compute(error_x, target_visible=True)
                                tilt_cmd = pid_y.compute(error_y, target_visible=True)

                                # 发送控制指令
                                send_gimbal_command(pan_cmd, tilt_cmd, int(smooth_center[0]), int(smooth_center[1]))

                        # 更新目标跟踪状态
                        target_tracker.update_target_status(target_found, rect_center)

                        # 如果目标丢失，执行搜索
                        if target_tracker.search_mode:
                            search_pan, search_tilt = target_tracker.get_search_command()
                            send_gimbal_command(search_pan, search_tilt)
                            print(f"搜索模式: Pan={search_pan:.1f}, 丢失帧数={target_tracker.target_lost_count}")

                            # 重置PID积分项，避免累积误差
                            if target_tracker.target_lost_count == target_tracker.max_lost_frames + 1:
                                pid_x.reset()
                                pid_y.reset()
                                rect_detector.reset_history()

                        target_locked = target_found
                        lost_count = target_tracker.target_lost_count

                    except Exception as e:
                        print(f"矩形检测错误: {e}")
                        target_tracker.update_target_status(False)

                # 绘制界面信息
                try:
                    # 中心十字线
                    img.draw_line(center_x - 20, center_y, center_x + 20, center_y, color=(255, 255, 255), thickness=2)
                    img.draw_line(center_x, center_y - 20, center_x, center_y + 20, color=(255, 255, 255), thickness=2)

                    # 状态信息
                    if system_mode == 1:
                        status_text = "TRACKING" if target_locked else "SEARCHING"
                        status_color = (0, 255, 0) if target_locked else (255, 255, 0)
                    else:
                        status_text = "ADJUSTING"
                        status_color = (255, 0, 255)

                    img.draw_string_advanced(10, 10, 30, f"Mode: {status_text}", color=status_color)
                    img.draw_string_advanced(10, 40, 25, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
                    img.draw_string_advanced(10, 65, 25, f"Frame: {frame_count}", color=(255, 255, 255))

                    if system_mode == 1:
                        if rect_center:
                            img.draw_string_advanced(10, 90, 25, f"Target: ({int(rect_center[0])}, {int(rect_center[1])})", color=(0, 255, 0))
                            img.draw_string_advanced(10, 115, 25, f"Error: ({error_x:.1f}, {error_y:.1f})", color=(255, 255, 255))
                            img.draw_string_advanced(10, 140, 25, f"Gimbal: Pan={pan_cmd:.2f}, Tilt={tilt_cmd:.2f}", color=(255, 0, 255))
                            img.draw_string_advanced(10, 165, 25, f"Confidence: {confidence:.3f}", color=(0, 255, 255))

                            # 显示目标稳定性
                            if target_tracker.is_target_stable():
                                img.draw_string_advanced(10, 190, 25, "Status: STABLE", color=(0, 255, 0))
                            else:
                                img.draw_string_advanced(10, 190, 25, "Status: TRACKING", color=(255, 255, 0))
                        else:
                            if target_tracker.search_mode:
                                img.draw_string_advanced(10, 90, 25, "Status: SEARCHING", color=(255, 0, 0))
                                search_pan, search_tilt = target_tracker.get_search_command()
                                img.draw_string_advanced(10, 115, 25, f"Search: Pan={search_pan:.1f}", color=(255, 128, 0))
                            else:
                                img.draw_string_advanced(10, 90, 25, "Status: LOST", color=(255, 255, 0))

                        # 操作提示和系统信息
                        img.draw_string_advanced(10, 380, 25, "按键: 调阈值", color=(255, 255, 255))
                        img.draw_string_advanced(10, 405, 25, f"阈值: {threshold_dict['rect']}", color=(0, 255, 255))
                        img.draw_string_advanced(10, 430, 25, f"丢失: {lost_count}/{max_lost_frames}", color=(255, 255, 0))
                        img.draw_string_advanced(10, 455, 25, f"历史点: {len(rect_detector.history_centers)}", color=(128, 255, 128))

                except Exception as e:
                    print(f"绘制界面错误: {e}")

                # 显示图像
                show_img_to_screen(img)

                # 定期状态报告
                if frame_count % 1000 == 0:
                    print(f"系统运行中... 模式: {system_mode}, 帧数: {frame_count}, FPS: {clock.fps():.1f}")

                time.sleep_ms(1)

            except KeyboardInterrupt:
                print("检测到键盘中断")
                running = False
                break
            except Exception as e:
                print(f"主循环错误: {e}")
                print("遇到错误但继续运行...")
                running = False
                time.sleep_ms(100)
                continue

        print("主循环已退出")

    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"系统严重错误: {e}")
        print("程序即将退出")
    finally:
        cleanup()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("程序中断")
    except Exception as e:
        print(f"未处理的异常: {e}")
    finally:
        cleanup()
        print("程序退出")
