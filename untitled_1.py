# K230矩形跟踪系统 - 优化版本
# 功能：矩形跟踪 + 脱机调矩形阈值 + 多角度稳定识别
# 按键进入阈值调整模式，默认一直处于识别矩形模式
# 阈值掉电不丢失，多角度识别优化

import time
import os
import json

# 导入模块
from media.sensor import *
from media.display import *
from media.media import *
from machine import FPIOA, Pin, UART, TOUCH
from time import ticks_ms
import image
import math

print("模块导入成功")

# 全局变量
sensor = None
running = True

# 阈值配置文件路径
THRESHOLD_CONFIG_FILE = "/root/threshold_config.json"

def load_threshold_config():
    """加载阈值配置"""
    try:
        if os.path.exists(THRESHOLD_CONFIG_FILE):
            with open(THRESHOLD_CONFIG_FILE, 'r') as f:
                config = json.load(f)
                # 确保阈值是元组格式
                if 'rect' in config:
                    config['rect'] = [tuple(t) if isinstance(t, list) else t for t in config['rect']]
                print(f"加载阈值配置: {config}")
                return config
        else:
            # 默认配置
            default_config = {'rect': [(59, 246)]}
            save_threshold_config(default_config)
            return default_config
    except Exception as e:
        print(f"加载阈值配置失败: {e}")
        return {'rect': [(59, 246)]}

def save_threshold_config(threshold_dict):
    """保存阈值配置"""
    try:
        with open(THRESHOLD_CONFIG_FILE, 'w') as f:
            json.dump(threshold_dict, f)
        print(f"保存阈值配置: {threshold_dict}")
    except Exception as e:
        print(f"保存阈值配置失败: {e}")

def cleanup():
    global sensor, running
    print("开始清理...")
    running = False
    try:
        if sensor:
            sensor.stop()
            print("摄像头已停止")
        Display.deinit()
        print("显示系统已关闭")
        MediaManager.deinit()
        print("媒体管理器已关闭")
    except Exception as e:
        print(f"清理过程中出错: {e}")
    print("清理完成")

def main():
    global sensor, running

    try:
        print("=== K230矩形跟踪系统 (集成调阈值功能) ===")

        # 1. 摄像头初始化
        print("初始化摄像头...")
        sensor = Sensor(width=1920, height=1080)
        sensor.reset()
        sensor.set_framesize(width=1920, height=1080)
        sensor.set_pixformat(Sensor.RGB565)

        # 2. 显示初始化
        print("初始化显示...")
        try:
            Display.init(Display.ST7701, to_ide=False, width=800, height=480)  # 使用LCD屏幕
            print("LCD显示初始化完成")
        except:
            # 备用显示方式
            Display.init(Display.LT9611, to_ide=False)
            print("备用显示初始化完成")

        # 3. 媒体管理器
        MediaManager.init()
        sensor.run()

        # 4. 硬件配置
        print("配置硬件...")
        fpioa = FPIOA()
        fpioa.set_function(5, FPIOA.UART2_TXD)
        fpioa.set_function(6, FPIOA.UART2_RXD)
        fpioa.set_function(53, FPIOA.GPIO53)

        key = Pin(53, Pin.IN, Pin.PULL_DOWN)
        uart2 = UART(UART.UART2, 115200)
        clock = time.clock()

        # 5. 触摸屏初始化
        tp = TOUCH(0)
        touch_counter = 0

        # 6. 内外矩形框精准识别算法
        class DualRectDetector:
            def __init__(self):
                self.history_centers = []
                self.history_size = 8
                self.confidence_threshold = 0.75  # 提高阈值
                self.last_good_center = None
                self.inner_outer_ratio_range = (0.3, 0.8)  # 内外矩形面积比范围
                self.concentricity_threshold = 20  # 同心度阈值(像素)
                self.stable_detection_count = 0
                self.min_stable_count = 3  # 需要连续3帧稳定检测

            def find_dual_rectangles(self, rects):
                """寻找内外矩形框对"""
                if len(rects) < 2:
                    return None, None, 0

                best_outer = None
                best_inner = None
                best_confidence = 0

                # 按面积排序，大的在前
                sorted_rects = sorted(rects, key=lambda r: r.w() * r.h(), reverse=True)

                for i, outer_rect in enumerate(sorted_rects):
                    outer_area = outer_rect.w() * outer_rect.h()
                    outer_center_x = outer_rect.x() + outer_rect.w() / 2
                    outer_center_y = outer_rect.y() + outer_rect.h() / 2

                    # 寻找可能的内矩形
                    for j, inner_rect in enumerate(sorted_rects[i+1:], i+1):
                        inner_area = inner_rect.w() * inner_rect.h()
                        inner_center_x = inner_rect.x() + inner_rect.w() / 2
                        inner_center_y = inner_rect.y() + inner_rect.h() / 2

                        # 检查面积比
                        area_ratio = inner_area / outer_area if outer_area > 0 else 0
                        if not (self.inner_outer_ratio_range[0] <= area_ratio <= self.inner_outer_ratio_range[1]):
                            continue

                        # 检查同心度
                        center_distance = math.sqrt((outer_center_x - inner_center_x)**2 +
                                                  (outer_center_y - inner_center_y)**2)
                        if center_distance > self.concentricity_threshold:
                            continue

                        # 检查内矩形是否在外矩形内部
                        if not self.is_rect_inside(inner_rect, outer_rect):
                            continue

                        # 计算双矩形置信度
                        confidence = self.calculate_dual_rect_confidence(
                            outer_rect, inner_rect, area_ratio, center_distance)

                        if confidence > best_confidence:
                            best_confidence = confidence
                            best_outer = outer_rect
                            best_inner = inner_rect

                return best_outer, best_inner, best_confidence

            def is_rect_inside(self, inner_rect, outer_rect):
                """检查内矩形是否在外矩形内部"""
                return (inner_rect.x() >= outer_rect.x() and
                        inner_rect.y() >= outer_rect.y() and
                        inner_rect.x() + inner_rect.w() <= outer_rect.x() + outer_rect.w() and
                        inner_rect.y() + inner_rect.h() <= outer_rect.y() + outer_rect.h())

            def calculate_dual_rect_confidence(self, outer_rect, inner_rect, area_ratio, center_distance):
                """计算双矩形置信度"""
                # 面积比分数
                ideal_ratio = 0.5  # 理想的内外面积比
                ratio_diff = abs(area_ratio - ideal_ratio) / ideal_ratio
                ratio_score = max(0, 1.0 - ratio_diff)

                # 同心度分数
                concentricity_score = max(0, 1.0 - center_distance / self.concentricity_threshold)

                # 外矩形规整度
                outer_area = outer_rect.w() * outer_rect.h()
                outer_perimeter = 2 * (outer_rect.w() + outer_rect.h())
                outer_shape_score = (4 * math.pi * outer_area) / (outer_perimeter * outer_perimeter) if outer_perimeter > 0 else 0

                # 内矩形规整度
                inner_area = inner_rect.w() * inner_rect.h()
                inner_perimeter = 2 * (inner_rect.w() + inner_rect.h())
                inner_shape_score = (4 * math.pi * inner_area) / (inner_perimeter * inner_perimeter) if inner_perimeter > 0 else 0

                # 尺寸合理性
                outer_size = math.sqrt(outer_area)
                size_score = 1.0 if 50 <= outer_size <= 200 else 0.5

                # 综合置信度
                confidence = (ratio_score * 0.3 + concentricity_score * 0.3 +
                            outer_shape_score * 0.2 + inner_shape_score * 0.1 + size_score * 0.1)

                return confidence

            def calculate_precise_center(self, outer_rect, inner_rect):
                """基于内外矩形计算精确中心"""
                # 外矩形中心
                outer_center_x = outer_rect.x() + outer_rect.w() / 2
                outer_center_y = outer_rect.y() + outer_rect.h() / 2

                # 内矩形中心
                inner_center_x = inner_rect.x() + inner_rect.w() / 2
                inner_center_y = inner_rect.y() + inner_rect.h() / 2

                # 加权平均，内矩形权重更高（更精确）
                precise_x = outer_center_x * 0.3 + inner_center_x * 0.7
                precise_y = outer_center_y * 0.3 + inner_center_y * 0.7

                return (precise_x, precise_y)

            def smooth_center(self, new_center):
                """超高精度中心点平滑"""
                if new_center is None:
                    return self.last_good_center

                self.history_centers.append(new_center)
                if len(self.history_centers) > self.history_size:
                    self.history_centers.pop(0)

                if len(self.history_centers) == 1:
                    self.last_good_center = new_center
                    return new_center

                # 自适应平滑因子
                if self.stable_detection_count >= self.min_stable_count:
                    alpha = 0.2  # 稳定时更平滑
                else:
                    alpha = 0.4  # 不稳定时更快响应

                # 指数加权移动平均
                smooth_x = new_center[0] * alpha
                smooth_y = new_center[1] * alpha

                total_weight = alpha
                for i in range(len(self.history_centers) - 2, -1, -1):
                    weight = alpha * (1 - alpha) ** (len(self.history_centers) - 1 - i)
                    smooth_x += self.history_centers[i][0] * weight
                    smooth_y += self.history_centers[i][1] * weight
                    total_weight += weight

                if total_weight > 0:
                    smooth_x /= total_weight
                    smooth_y /= total_weight

                result = (smooth_x, smooth_y)
                self.last_good_center = result
                return result

            def detect_dual_rect_target(self, rects, img_width, img_height):
                """检测双矩形目标"""
                if not rects:
                    self.stable_detection_count = 0
                    return None, None, 0, None

                # 寻找最佳内外矩形对
                outer_rect, inner_rect, confidence = self.find_dual_rectangles(rects)

                if outer_rect and inner_rect and confidence > self.confidence_threshold:
                    # 计算精确中心
                    precise_center = self.calculate_precise_center(outer_rect, inner_rect)

                    # 增加稳定检测计数
                    self.stable_detection_count += 1

                    return outer_rect, inner_rect, confidence, precise_center
                else:
                    # 重置稳定计数
                    self.stable_detection_count = 0
                    return None, None, 0, None

            def is_detection_stable(self):
                """检查检测是否稳定"""
                return self.stable_detection_count >= self.min_stable_count

            def reset_history(self):
                """重置历史记录"""
                self.history_centers = []
                self.last_good_center = None
                self.stable_detection_count = 0

        # 7. 优化的PID控制器
        class AdaptivePID:
            def __init__(self, kp=0.3, ki=0.05, kd=0.15, max_output=20.0):
                self.kp = kp
                self.ki = ki
                self.kd = kd
                self.max_output = max_output
                self.prev_error = 0
                self.integral = 0
                self.prev_time = ticks_ms()
                self.error_history = []
                self.max_history = 10

            def reset(self):
                """重置PID状态"""
                self.prev_error = 0
                self.integral = 0
                self.error_history = []
                self.prev_time = ticks_ms()

            def compute(self, error, target_visible=True):
                current_time = ticks_ms()
                dt = (current_time - self.prev_time) / 1000.0
                if dt <= 0: dt = 0.01

                # 记录误差历史
                self.error_history.append(abs(error))
                if len(self.error_history) > self.max_history:
                    self.error_history.pop(0)

                # 自适应参数调整
                avg_error = sum(self.error_history) / len(self.error_history) if self.error_history else 0

                # 根据误差大小和目标可见性调整参数
                if not target_visible:
                    # 目标不可见时，停止积分，减小输出
                    kp_adj = self.kp * 0.1
                    ki_adj = 0
                    kd_adj = self.kd * 0.5
                    max_out = self.max_output * 0.3
                elif avg_error > 50:
                    # 大误差时，增强比例控制
                    kp_adj = self.kp * 1.2
                    ki_adj = self.ki * 0.5
                    kd_adj = self.kd * 1.1
                    max_out = self.max_output
                elif avg_error < 10:
                    # 小误差时，增强积分和微分
                    kp_adj = self.kp * 0.8
                    ki_adj = self.ki * 1.2
                    kd_adj = self.kd * 1.3
                    max_out = self.max_output * 0.7
                else:
                    # 正常情况
                    kp_adj = self.kp
                    ki_adj = self.ki
                    kd_adj = self.kd
                    max_out = self.max_output

                # PID计算
                if target_visible:
                    self.integral += error * dt
                    # 积分限幅
                    integral_limit = max_out / (ki_adj + 0.001)
                    if self.integral > integral_limit:
                        self.integral = integral_limit
                    elif self.integral < -integral_limit:
                        self.integral = -integral_limit

                derivative = (error - self.prev_error) / dt
                output = kp_adj * error + ki_adj * self.integral + kd_adj * derivative

                # 输出限幅
                if output > max_out:
                    output = max_out
                elif output < -max_out:
                    output = -max_out

                self.prev_error = error
                self.prev_time = current_time
                return output

        # 8. 步进电机智能跟踪管理器
        class StepperMotorTracker:
            def __init__(self):
                self.target_lost_count = 0
                self.max_lost_frames = 10  # 减少到10帧，更快响应
                self.motor_active = False  # 步进电机是否激活
                self.last_known_position = None
                self.stable_count = 0
                self.min_stable_frames = 3  # 减少稳定帧数要求
                self.no_target_hold_time = 0  # 无目标保持时间
                self.max_hold_time = 30  # 最大保持时间(帧)

            def update_target_status(self, target_found, target_center=None, detection_stable=False):
                if target_found and detection_stable:
                    # 目标稳定检测到
                    self.target_lost_count = 0
                    self.motor_active = True  # 激活步进电机
                    self.last_known_position = target_center
                    self.stable_count += 1
                    self.no_target_hold_time = 0

                elif target_found and not detection_stable:
                    # 目标检测到但不稳定，保持当前状态
                    self.target_lost_count = 0
                    self.no_target_hold_time = 0
                    # 不改变motor_active状态

                else:
                    # 目标丢失
                    self.target_lost_count += 1
                    self.stable_count = 0
                    self.no_target_hold_time += 1

                    # 步进电机控制策略：识别不到时不动
                    if self.target_lost_count >= self.max_lost_frames:
                        self.motor_active = False  # 停止步进电机

            def should_move_motor(self):
                """判断是否应该移动步进电机"""
                return self.motor_active

            def get_motor_command(self, pan_cmd, tilt_cmd):
                """获取步进电机指令"""
                if self.should_move_motor():
                    return pan_cmd, tilt_cmd
                else:
                    return 0, 0  # 不移动

            def is_target_stable(self):
                return self.stable_count >= self.min_stable_frames

            def get_status_info(self):
                """获取状态信息"""
                if self.motor_active:
                    if self.is_target_stable():
                        return "STABLE_TRACKING"
                    else:
                        return "ACTIVE_TRACKING"
                else:
                    if self.target_lost_count > 0:
                        return "MOTOR_STOPPED"
                    else:
                        return "INITIALIZING"

        rect_detector = DualRectDetector()
        pid_x = AdaptivePID(kp=0.2, ki=0.02, kd=0.1, max_output=12.0)  # 进一步降低增益
        pid_y = AdaptivePID(kp=0.2, ki=0.02, kd=0.1, max_output=12.0)
        target_tracker = StepperMotorTracker()

        # 9. 优化的云台控制函数
        def send_gimbal_command(pan_cmd, tilt_cmd, rect_x=0, rect_y=0, force_send=False):
            try:
                pan_cmd = float(pan_cmd) if pan_cmd is not None else 0.0
                tilt_cmd = float(tilt_cmd) if tilt_cmd is not None else 0.0
                rect_x = int(rect_x) if rect_x is not None else 0
                rect_y = int(rect_y) if rect_y is not None else 0

                # 限制指令幅度，防止过大的转动
                max_single_cmd = 8.0  # 单次最大指令幅度
                pan_cmd = max(-max_single_cmd, min(max_single_cmd, pan_cmd))
                tilt_cmd = max(-max_single_cmd, min(max_single_cmd, tilt_cmd))

                # 死区处理，避免小幅抖动
                dead_zone = 0.5
                if abs(pan_cmd) < dead_zone and not force_send:
                    pan_cmd = 0.0
                if abs(tilt_cmd) < dead_zone and not force_send:
                    tilt_cmd = 0.0

                pan_int = int(pan_cmd * 10) + 1000
                tilt_int = int(tilt_cmd * 10) + 1000

                rx_order = [0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFE]
                rx_order[1] = (pan_int >> 8) & 0xFF
                rx_order[2] = pan_int & 0xFF
                rx_order[3] = (tilt_int >> 8) & 0xFF
                rx_order[4] = tilt_int & 0xFF
                rx_order[5] = (rect_x >> 8) & 0xFF
                rx_order[6] = rect_x & 0xFF
                rx_order[7] = (rect_y >> 8) & 0xFF
                rx_order[8] = rect_y & 0xFF

                uart2.write(bytes(rx_order))
                if abs(pan_cmd) > 0.1 or abs(tilt_cmd) > 0.1 or force_send:
                    print(f"云台指令: Pan={pan_cmd:.2f}, Tilt={tilt_cmd:.2f}")
            except Exception as e:
                print(f"发送指令失败: {e}")

        # 10. 云台归零函数
        def gimbal_reset():
            """云台归零，解决上电右转问题"""
            print("云台归零中...")
            for i in range(10):
                send_gimbal_command(0, 0, force_send=True)
                time.sleep_ms(50)
            print("云台归零完成")

        # 8. 显示图像到屏幕函数
        def show_img_to_screen(img):
            try:
                if img.height() > 480 or img.width() > 800:
                    scale = max(img.height() // 480, img.width() // 800) + 1
                    img.midpoint_pool(scale, scale)
                Display.show_image(img, x=(800-img.width())//2, y=(480-img.height())//2)
            except Exception as e:
                print(f"显示图像失败: {e}")

        # 9. 阈值调整功能
        def threshold_adjust_mode(threshold_dict):
            print("进入阈值调整模式")

            button_color = (150, 150, 150)
            text_color = (0, 0, 0)

            # 创建按钮界面
            ui_img = image.Image(800, 480, image.RGB565)
            ui_img.draw_rectangle(0, 0, 800, 480, color=(255, 255, 255), thickness=2, fill=True)

            # 按钮--返回
            ui_img.draw_rectangle(0, 0, 160, 40, color=button_color, thickness=2, fill=True)
            ui_img.draw_string_advanced(50, 0, 30, "返回", color=text_color)

            # 按钮--归位
            ui_img.draw_rectangle(0, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
            ui_img.draw_string_advanced(50, 480-40, 30, "归位", color=text_color)

            # 按钮--保存
            ui_img.draw_rectangle(800-160, 480-40, 160, 40, color=button_color, thickness=2, fill=True)
            ui_img.draw_string_advanced(800-110, 480-40, 30, "保存", color=text_color)

            # 绘制滑块控制按钮 (只需要2个，控制矩形阈值的上下限)
            for j in [0, 800 - 160]:
                for i in range(60, 180, 60):  # 只需要2行
                    ui_img.draw_rectangle(j, i, 160, 40, color=button_color, thickness=2, fill=True)

            # 添加标签
            ui_img.draw_string_advanced(10, 70, 25, "下限-", color=text_color)
            ui_img.draw_string_advanced(650, 70, 25, "下限+", color=text_color)
            ui_img.draw_string_advanced(10, 130, 25, "上限-", color=text_color)
            ui_img.draw_string_advanced(650, 130, 25, "上限+", color=text_color)

            def which_button(x, y):
                if x < 160:
                    if y < 40:
                        return "return"
                    if y > 480 - 40:
                        return "reset"
                    if 60 <= y < 100:
                        return "0"  # 下限-
                    if 120 <= y < 160:
                        return "1"  # 上限-
                elif x > 800-160:
                    if y > 480 - 40:
                        return "save"
                    if 60 <= y < 100:
                        return "2"  # 下限+
                    if 120 <= y < 160:
                        return "3"  # 上限+
                return None

            # 矩形阈值 [下限, 上限]
            threshold_current = [59, 246]
            cut_roi = (540, 300, 480, 480)

            while True:
                try:
                    # 获取预览图像
                    preview_img = sensor.snapshot(chn=CAM_CHN_ID_0)
                    preview_img = preview_img.copy(roi=cut_roi)

                    # 应用当前阈值
                    preview_gray = preview_img.to_grayscale()
                    preview_binary = preview_gray.binary([threshold_current])
                    preview_rgb = preview_binary.to_rgb565()

                    # 将预览图像绘制到UI上
                    ui_img.draw_image(preview_rgb, (800-preview_rgb.width()) // 2, (480-preview_rgb.height()) // 2)

                    # 显示当前阈值
                    ui_img.draw_rectangle(200, 200, 400, 80, color=(255, 255, 255), thickness=2, fill=True)
                    ui_img.draw_string_advanced(220, 210, 30, f"矩形阈值: [{threshold_current[0]}, {threshold_current[1]}]", color=(0, 0, 0))

                    # 处理触摸
                    points = tp.read()
                    if len(points) > 0:
                        button = which_button(points[0].x, points[0].y)
                        if button:
                            if button == "return":
                                print("退出阈值调整模式")
                                return False
                            elif button == "reset":
                                threshold_current = [0, 255]
                                print("阈值已重置")
                            elif button == "save":
                                threshold_dict['rect'] = [threshold_current.copy()]
                                save_threshold_config(threshold_dict)  # 保存到文件
                                print(f"保存阈值: {threshold_current}")
                                ui_img.draw_rectangle(200, 300, 400, 40, color=(0, 255, 0), thickness=2, fill=True)
                                ui_img.draw_string_advanced(300, 310, 30, "保存成功!", color=(0, 0, 0))
                            elif button == "0":  # 下限-
                                threshold_current[0] = max(0, threshold_current[0] - 2)
                            elif button == "1":  # 上限-
                                threshold_current[1] = max(threshold_current[0], threshold_current[1] - 2)
                            elif button == "2":  # 下限+
                                threshold_current[0] = min(threshold_current[1], threshold_current[0] + 2)
                            elif button == "3":  # 上限+
                                threshold_current[1] = min(255, threshold_current[1] + 2)

                            time.sleep_ms(100)  # 防止连续触发

                    show_img_to_screen(ui_img)

                except Exception as e:
                    print(f"阈值调整模式错误: {e}")
                    continue

        # 10. 系统参数
        CAM_CHN_ID_0 = 0  # 摄像头通道ID
        img_width = 480
        img_height = 480
        center_x = img_width // 2
        center_y = img_height // 2
        cut_roi = (540, 300, 480, 480)

        target_locked = False
        lost_count = 0
        max_lost_frames = 20
        frame_count = 0

        # 系统状态: 1=跟踪模式(默认), 2=阈值调整模式
        system_mode = 1

        # 加载保存的阈值配置
        threshold_dict = load_threshold_config()

        print("开始主循环...")
        print("按键进入阈值调整模式")
        print("默认处于矩形跟踪模式")

        # 系统初始化 - 云台归零
        print("正在初始化云台...")
        gimbal_reset()
        time.sleep_ms(1000)  # 等待云台稳定

        # 11. 主循环
        while running:
            try:
                clock.tick()

                # 获取图像
                img = sensor.snapshot(chn=CAM_CHN_ID_0)
                if img is None:
                    print("获取图像失败，继续尝试...")
                    time.sleep_ms(10)
                    continue

                img = img.copy(roi=cut_roi)
                frame_count += 1

                # 按键处理 - 进入阈值调整模式
                if key.value() == 1:
                    if system_mode == 1:  # 从跟踪模式进入阈值调整模式
                        print("进入阈值调整模式")
                        system_mode = 2
                        threshold_adjust_mode(threshold_dict)
                        system_mode = 1  # 调整完成后返回跟踪模式
                        time.sleep_ms(500)  # 防止按键重复触发

                # 内外矩形框精准跟踪模式
                if system_mode == 1:
                    rect_center = None
                    pan_cmd = 0.0
                    tilt_cmd = 0.0
                    error_x = 0.0
                    error_y = 0.0
                    confidence = 0.0
                    target_found = False
                    outer_rect = None
                    inner_rect = None

                    try:
                        # 内外矩形框检测
                        img_rect = img.to_grayscale(copy=True)

                        # 多阈值检测，专门针对内外矩形框
                        all_rects = []
                        for threshold in threshold_dict['rect']:
                            try:
                                binary_img = img_rect.binary([threshold])

                                # 针对内外矩形框的形态学操作
                                binary_img.erode(1)    # 去除小噪声
                                binary_img.dilate(3)   # 连接断开的边缘
                                binary_img.erode(2)    # 恢复原始形状

                                # 多尺度检测，重点检测矩形框
                                rects1 = binary_img.find_rects(threshold=1500)   # 内矩形
                                rects2 = binary_img.find_rects(threshold=4000)   # 中等矩形
                                rects3 = binary_img.find_rects(threshold=8000)   # 外矩形
                                rects4 = binary_img.find_rects(threshold=15000)  # 大外矩形

                                all_rects.extend(rects1)
                                all_rects.extend(rects2)
                                all_rects.extend(rects3)
                                all_rects.extend(rects4)
                            except Exception as e:
                                print(f"阈值 {threshold} 检测失败: {e}")
                                continue

                        if all_rects:
                            # 使用双矩形检测算法
                            outer_rect, inner_rect, confidence, precise_center = rect_detector.detect_dual_rect_target(
                                all_rects, img_width, img_height)

                            if outer_rect and inner_rect and confidence > rect_detector.confidence_threshold:
                                target_found = True

                                # 根据置信度选择颜色
                                if confidence > 0.9:
                                    rect_color = (0, 255, 0)    # 绿色 - 完美识别
                                elif confidence > 0.8:
                                    rect_color = (128, 255, 0)  # 黄绿 - 优秀识别
                                elif confidence > 0.75:
                                    rect_color = (255, 255, 0)  # 黄色 - 良好识别
                                else:
                                    rect_color = (255, 128, 0)  # 橙色 - 一般识别

                                # 绘制外矩形框
                                outer_corners = outer_rect.corners()
                                for i in range(4):
                                    next_i = (i + 1) % 4
                                    img.draw_line(outer_corners[i][0], outer_corners[i][1],
                                                outer_corners[next_i][0], outer_corners[next_i][1],
                                                color=rect_color, thickness=3)

                                # 绘制内矩形框
                                inner_corners = inner_rect.corners()
                                for i in range(4):
                                    next_i = (i + 1) % 4
                                    img.draw_line(inner_corners[i][0], inner_corners[i][1],
                                                inner_corners[next_i][0], inner_corners[next_i][1],
                                                color=(255, 0, 255), thickness=2)  # 紫色内框

                                # 使用超高精度中心点平滑
                                smooth_center = rect_detector.smooth_center(precise_center)
                                rect_center = smooth_center

                                # 绘制精确中心点
                                img.draw_circle(int(smooth_center[0]), int(smooth_center[1]), 4,
                                               color=(255, 0, 0), thickness=2, fill=True)
                                img.draw_line(center_x, center_y, int(smooth_center[0]), int(smooth_center[1]),
                                            color=(255, 0, 0), thickness=2)

                                # 计算精确偏差
                                error_x = smooth_center[0] - center_x
                                error_y = smooth_center[1] - center_y

                                # 自适应PID控制
                                pan_cmd = pid_x.compute(error_x, target_visible=True)
                                tilt_cmd = pid_y.compute(error_y, target_visible=True)

                        # 更新步进电机跟踪状态
                        detection_stable = rect_detector.is_detection_stable()
                        target_tracker.update_target_status(target_found, rect_center, detection_stable)

                        # 步进电机智能控制：只有在稳定检测到目标时才移动
                        final_pan_cmd, final_tilt_cmd = target_tracker.get_motor_command(pan_cmd, tilt_cmd)

                        if target_tracker.should_move_motor():
                            # 发送步进电机控制指令
                            send_gimbal_command(final_pan_cmd, final_tilt_cmd,
                                              int(rect_center[0]) if rect_center else 0,
                                              int(rect_center[1]) if rect_center else 0)
                        else:
                            # 步进电机停止，发送零指令
                            send_gimbal_command(0, 0, force_send=True)

                            # 重置PID积分项，避免累积误差
                            if target_tracker.target_lost_count == target_tracker.max_lost_frames:
                                pid_x.reset()
                                pid_y.reset()
                                rect_detector.reset_history()

                        target_locked = target_found and detection_stable
                        lost_count = target_tracker.target_lost_count

                    except Exception as e:
                        print(f"双矩形检测错误: {e}")
                        target_tracker.update_target_status(False, None, False)

                # 绘制界面信息
                try:
                    # 中心十字线
                    img.draw_line(center_x - 20, center_y, center_x + 20, center_y, color=(255, 255, 255), thickness=2)
                    img.draw_line(center_x, center_y - 20, center_x, center_y + 20, color=(255, 255, 255), thickness=2)

                    # 状态信息
                    if system_mode == 1:
                        tracker_status = target_tracker.get_status_info()
                        if tracker_status == "STABLE_TRACKING":
                            status_text = "DUAL_RECT_STABLE"
                            status_color = (0, 255, 0)
                        elif tracker_status == "ACTIVE_TRACKING":
                            status_text = "DUAL_RECT_TRACKING"
                            status_color = (128, 255, 0)
                        elif tracker_status == "MOTOR_STOPPED":
                            status_text = "MOTOR_STOPPED"
                            status_color = (255, 128, 0)
                        else:
                            status_text = "INITIALIZING"
                            status_color = (255, 255, 0)
                    else:
                        status_text = "ADJUSTING"
                        status_color = (255, 0, 255)

                    img.draw_string_advanced(10, 10, 30, f"Mode: {status_text}", color=status_color)
                    img.draw_string_advanced(10, 40, 25, f"FPS: {clock.fps():.1f}", color=(255, 255, 255))
                    img.draw_string_advanced(10, 65, 25, f"Frame: {frame_count}", color=(255, 255, 255))

                    if system_mode == 1:
                        if rect_center and target_tracker.should_move_motor():
                            img.draw_string_advanced(10, 90, 25, f"Target: ({int(rect_center[0])}, {int(rect_center[1])})", color=(0, 255, 0))
                            img.draw_string_advanced(10, 115, 25, f"Error: ({error_x:.1f}, {error_y:.1f})", color=(255, 255, 255))

                            # 显示实际发送的指令
                            final_pan, final_tilt = target_tracker.get_motor_command(pan_cmd, tilt_cmd)
                            img.draw_string_advanced(10, 140, 25, f"Motor: Pan={final_pan:.2f}, Tilt={final_tilt:.2f}", color=(255, 0, 255))
                            img.draw_string_advanced(10, 165, 25, f"Confidence: {confidence:.3f}", color=(0, 255, 255))

                            # 显示双矩形检测信息
                            if outer_rect and inner_rect:
                                outer_area = outer_rect.w() * outer_rect.h()
                                inner_area = inner_rect.w() * inner_rect.h()
                                area_ratio = inner_area / outer_area if outer_area > 0 else 0
                                img.draw_string_advanced(10, 190, 25, f"Dual: Outer={int(outer_area)}, Ratio={area_ratio:.2f}", color=(128, 255, 128))

                            # 显示检测稳定性
                            if rect_detector.is_detection_stable():
                                img.draw_string_advanced(10, 215, 25, "Detection: STABLE", color=(0, 255, 0))
                            else:
                                img.draw_string_advanced(10, 215, 25, "Detection: UNSTABLE", color=(255, 255, 0))
                        else:
                            # 目标丢失或步进电机停止
                            img.draw_string_advanced(10, 90, 25, "Status: NO_TARGET", color=(255, 0, 0))
                            img.draw_string_advanced(10, 115, 25, "Motor: STOPPED", color=(255, 128, 0))
                            img.draw_string_advanced(10, 140, 25, f"Lost Frames: {lost_count}", color=(255, 255, 0))

                        # 操作提示和系统信息
                        img.draw_string_advanced(10, 380, 25, "按键: 调阈值", color=(255, 255, 255))
                        img.draw_string_advanced(10, 405, 25, f"阈值: {threshold_dict['rect']}", color=(0, 255, 255))
                        img.draw_string_advanced(10, 430, 25, f"步进电机: {'激活' if target_tracker.should_move_motor() else '停止'}", color=(0, 255, 0) if target_tracker.should_move_motor() else (255, 0, 0))
                        img.draw_string_advanced(10, 455, 25, f"稳定计数: {rect_detector.stable_detection_count}", color=(128, 255, 128))

                except Exception as e:
                    print(f"绘制界面错误: {e}")

                # 显示图像
                show_img_to_screen(img)

                # 定期状态报告
                if frame_count % 1000 == 0:
                    print(f"系统运行中... 模式: {system_mode}, 帧数: {frame_count}, FPS: {clock.fps():.1f}")

                time.sleep_ms(1)

            except KeyboardInterrupt:
                print("检测到键盘中断")
                running = False
                break
            except Exception as e:
                print(f"主循环错误: {e}")
                print("遇到错误但继续运行...")
                running = False
                time.sleep_ms(100)
                continue

        print("主循环已退出")

    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"系统严重错误: {e}")
        print("程序即将退出")
    finally:
        cleanup()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("程序中断")
    except Exception as e:
        print(f"未处理的异常: {e}")
    finally:
        cleanup()
        print("程序退出")
